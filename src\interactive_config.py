"""
対話式設定入力モジュール
ユーザーからの設定入力を受け付け、設定を動的に更新する機能を提供
"""

import re
from datetime import datetime
from typing import List, Optional, Tuple
from src.config import FilterConfig
from src.logger import ApplicationLogger


class InteractiveConfigInput:
    """対話式設定入力クラス"""
    
    def __init__(self, logger: ApplicationLogger):
        """
        初期化
        
        Args:
            logger: ログ
        """
        self.logger = logger
    
    def prompt_for_filters(self, current_filters: FilterConfig) -> FilterConfig:
        """
        フィルター設定を対話式で入力
        
        Args:
            current_filters: 現在のフィルター設定
            
        Returns:
            FilterConfig: 更新されたフィルター設定
        """
        print("\n" + "=" * 60)
        print("対話式設定入力")
        print("=" * 60)
        print("現在の設定を確認し、必要に応じて変更してください。")
        print("変更しない場合は Enter キーを押してください。")
        print("-" * 60)
        
        # 店舗コードの入力
        store_codes = self._prompt_store_codes(current_filters.store_codes)
        
        # 営業日の入力
        business_date = self._prompt_business_date(current_filters.business_date)
        
        # POS区分の入力（オプション）
        pos_kbn = self._prompt_pos_kbn(current_filters.pos_kbn)
        
        # 号車番号の入力（オプション）
        car_no = self._prompt_car_no(current_filters.car_no)
        
        # 更新された設定を作成
        updated_filters = FilterConfig(
            store_codes=store_codes,
            business_date=business_date,
            pos_kbn=pos_kbn,
            car_no=car_no
        )
        
        # 設定確認
        self._display_updated_settings(updated_filters)
        
        return updated_filters
    
    def _prompt_store_codes(self, current_codes: List[int]) -> List[int]:
        """店舗コードの入力プロンプト"""
        print(f"\n1. 対象店舗コード")
        print(f"   現在の設定: {current_codes}")
        print(f"   形式: カンマ区切りの数値 (例: 100,200,300)")
        print(f"   範囲: 1-9999の整数、最大100件")
        
        while True:
            try:
                user_input = input("   新しい店舗コード (Enter=変更なし): ").strip()
                
                if not user_input:
                    return current_codes
                
                # カンマ区切りで分割し、整数に変換
                codes = []
                for code_str in user_input.split(','):
                    code_str = code_str.strip()
                    if not code_str:
                        continue
                    
                    code = int(code_str)
                    if code < 1 or code > 9999:
                        raise ValueError(f"店舗コード {code} は範囲外です (1-9999)")
                    codes.append(code)
                
                if len(codes) == 0:
                    raise ValueError("少なくとも1つの店舗コードを指定してください")
                
                if len(codes) > 100:
                    raise ValueError("店舗コードは最大100件まで指定できます")
                
                # 重複を除去してソート
                codes = sorted(list(set(codes)))
                
                print(f"   → 設定: {codes}")
                return codes
                
            except ValueError as e:
                print(f"   エラー: {e}")
                print(f"   再入力してください。")
            except KeyboardInterrupt:
                print(f"\n   変更をキャンセルしました。")
                return current_codes
    
    def _prompt_business_date(self, current_date: str) -> str:
        """営業日の入力プロンプト"""
        print(f"\n2. 営業日")
        print(f"   現在の設定: {current_date}")
        print(f"   形式: YYYY/MM/DD")
        
        while True:
            try:
                user_input = input("   新しい営業日 (Enter=変更なし): ").strip()
                
                if not user_input:
                    return current_date
                
                # 日付形式の検証
                if not re.match(r'^\d{4}/\d{2}/\d{2}$', user_input):
                    raise ValueError("日付形式が正しくありません (YYYY/MM/DD)")
                
                # 日付として有効かチェック
                datetime.strptime(user_input, '%Y/%m/%d')
                
                print(f"   → 設定: {user_input}")
                return user_input
                
            except ValueError as e:
                print(f"   エラー: {e}")
                print(f"   再入力してください。")
            except KeyboardInterrupt:
                print(f"\n   変更をキャンセルしました。")
                return current_date
    
    def _prompt_pos_kbn(self, current_pos_kbn: Optional[int]) -> Optional[int]:
        """POS区分の入力プロンプト"""
        print(f"\n3. POS区分 (オプション)")
        print(f"   現在の設定: {current_pos_kbn if current_pos_kbn is not None else 'なし'}")
        print(f"   形式: 0-9の整数またはなし")
        print(f"   ※ SanshiNetSuperSaleテーブルの削除条件にのみ影響します")
        
        while True:
            try:
                user_input = input("   新しいPOS区分 (Enter=変更なし, 'null'=なし): ").strip()
                
                if not user_input:
                    return current_pos_kbn
                
                if user_input.lower() in ['null', 'none', 'なし', '']:
                    print(f"   → 設定: なし")
                    return None
                
                pos_kbn = int(user_input)
                if pos_kbn < 0 or pos_kbn > 9:
                    raise ValueError("POS区分は0-9の整数である必要があります")
                
                print(f"   → 設定: {pos_kbn}")
                return pos_kbn
                
            except ValueError as e:
                print(f"   エラー: {e}")
                print(f"   再入力してください。")
            except KeyboardInterrupt:
                print(f"\n   変更をキャンセルしました。")
                return current_pos_kbn
    
    def _prompt_car_no(self, current_car_no: Optional[int]) -> Optional[int]:
        """号車番号の入力プロンプト"""
        print(f"\n4. 号車番号 (オプション)")
        print(f"   現在の設定: {current_car_no if current_car_no is not None else 'なし'}")
        print(f"   形式: 0-9の整数またはなし")
        print(f"   ※ SanshiNetSuperSaleテーブルの削除条件にのみ影響します")
        
        while True:
            try:
                user_input = input("   新しい号車番号 (Enter=変更なし, 'null'=なし): ").strip()
                
                if not user_input:
                    return current_car_no
                
                if user_input.lower() in ['null', 'none', 'なし', '']:
                    print(f"   → 設定: なし")
                    return None
                
                car_no = int(user_input)
                if car_no < 0 or car_no > 9:
                    raise ValueError("号車番号は0-9の整数である必要があります")
                
                print(f"   → 設定: {car_no}")
                return car_no
                
            except ValueError as e:
                print(f"   エラー: {e}")
                print(f"   再入力してください。")
            except KeyboardInterrupt:
                print(f"\n   変更をキャンセルしました。")
                return current_car_no
    
    def _display_updated_settings(self, filters: FilterConfig) -> None:
        """更新された設定を表示"""
        print("\n" + "=" * 60)
        print("更新された設定")
        print("=" * 60)
        print(f"対象店舗コード: {filters.store_codes}")
        print(f"営業日: {filters.business_date}")
        print(f"POS区分: {filters.pos_kbn if filters.pos_kbn is not None else 'なし'}")
        print(f"号車番号: {filters.car_no if filters.car_no is not None else 'なし'}")
        print("=" * 60)
        
        # ログに記録
        self.logger.log_info("対話式設定入力完了")
        self.logger.log_config_loaded(
            filters.store_codes,
            filters.business_date,
            filters.pos_kbn,
            filters.car_no
        )
    
    def confirm_settings_update(self) -> bool:
        """設定更新の確認"""
        print(f"\nこの設定でクリーンアップ処理を実行しますか？")
        
        while True:
            try:
                response = input("実行しますか？ (Y/N): ").strip().upper()
                
                if response == 'Y':
                    self.logger.log_info("ユーザーが設定更新を承認")
                    return True
                elif response == 'N':
                    print("設定更新をキャンセルしました。")
                    self.logger.log_user_cancelled()
                    return False
                else:
                    print("Y または N を入力してください。")
                    
            except KeyboardInterrupt:
                print("\n\n設定更新をキャンセルしました。")
                self.logger.log_user_cancelled()
                return False
            except EOFError:
                print("\n\n設定更新をキャンセルしました。")
                self.logger.log_user_cancelled()
                return False
