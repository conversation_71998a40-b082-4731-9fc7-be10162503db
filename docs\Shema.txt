USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[SanshiNetSuperSale]    Script Date: 2025/08/14 15:26:33 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SanshiNetSuperSale](
	[Id] [uniqueidentifier] NOT NULL,
	[年月度] [nvarchar](6) NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗ｺｰﾄﾞ] [int] NOT NULL,
	[地区ｺｰﾄﾞ] [int] NOT NULL,
	[便NO] [int] NOT NULL,
	[号車NO] [int] NOT NULL,
	[号車名] [nvarchar](1) NOT NULL,
	[DLｺｰﾄﾞ] [int] NOT NULL,
	[配達順] [int] NOT NULL,
	[地図番号] [nvarchar](12) NOT NULL,
	[会員様ｺｰﾄﾞ] [int] NOT NULL,
	[PNO] [int] NOT NULL,
	[売上区分] [int] NOT NULL,
	[部門ｺｰﾄﾞ] [int] NOT NULL,
	[部類ｺｰﾄﾞ] [int] NOT NULL,
	[容器種別] [int] NOT NULL,
	[棚ｱﾄﾞﾚｽ] [nvarchar](8) NOT NULL,
	[JANｺｰﾄﾞ] [nvarchar](20) NOT NULL,
	[商品ｺｰﾄﾞ] [int] NOT NULL,
	[商品名] [nvarchar](60) NOT NULL,
	[数量] [int] NOT NULL,
	[売価] [int] NOT NULL,
	[原価] [decimal](18, 4) NOT NULL,
	[請求額] [int] NOT NULL,
	[税区分] [int] NOT NULL,
	[税率] [int] NOT NULL,
	[特売区分] [int] NOT NULL,
	[ﾐｯｸｽﾏｯﾁNO] [int] NOT NULL,
	[不定貫ﾏｰｸ] [nvarchar](2) NOT NULL,
	[注文時売価] [int] NOT NULL,
	[ｼﾞｬﾝﾙNO] [int] NOT NULL,
	[現金ﾌﾗｸﾞ] [int] NOT NULL,
	[更新時間] [nvarchar](19) NOT NULL,
	[IsProcessed] [bit] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[POS区分] [smallint] NOT NULL,
 CONSTRAINT [PK_SanshiNetSuperSale] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_年月度]  DEFAULT ('') FOR [年月度]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_店舗ｺｰﾄﾞ]  DEFAULT ((0)) FOR [店舗ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_地区ｺｰﾄﾞ]  DEFAULT ((0)) FOR [地区ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_便NO]  DEFAULT ((0)) FOR [便NO]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_号車NO]  DEFAULT ((0)) FOR [号車NO]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_号車名]  DEFAULT ('') FOR [号車名]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_DLｺｰﾄﾞ]  DEFAULT ((0)) FOR [DLｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_配達順]  DEFAULT ((0)) FOR [配達順]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_地図番号]  DEFAULT ('') FOR [地図番号]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_会員様ｺｰﾄﾞ]  DEFAULT ((0)) FOR [会員様ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_PNO]  DEFAULT ((0)) FOR [PNO]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_売上区分]  DEFAULT ((0)) FOR [売上区分]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_部門ｺｰﾄﾞ]  DEFAULT ((0)) FOR [部門ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_部類ｺｰﾄﾞ]  DEFAULT ((0)) FOR [部類ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_容器種別]  DEFAULT ((0)) FOR [容器種別]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_棚ｱﾄﾞﾚｽ]  DEFAULT ('') FOR [棚ｱﾄﾞﾚｽ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_JANｺｰﾄﾞ]  DEFAULT ('') FOR [JANｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_商品ｺｰﾄﾞ]  DEFAULT ((0)) FOR [商品ｺｰﾄﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_商品名]  DEFAULT ('') FOR [商品名]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_数量]  DEFAULT ((0)) FOR [数量]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_売価]  DEFAULT ((0)) FOR [売価]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_原価]  DEFAULT ((0)) FOR [原価]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_請求額]  DEFAULT ((0)) FOR [請求額]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_税区分]  DEFAULT ((0)) FOR [税区分]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_税率]  DEFAULT ((0)) FOR [税率]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_特売区分]  DEFAULT ((0)) FOR [特売区分]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_ﾐｯｸｽﾏｯﾁNO]  DEFAULT ((0)) FOR [ﾐｯｸｽﾏｯﾁNO]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_不定貫ﾏｰｸ]  DEFAULT ('') FOR [不定貫ﾏｰｸ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_注文時売価]  DEFAULT ((0)) FOR [注文時売価]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_ｼﾞｬﾝﾙNO]  DEFAULT ((0)) FOR [ｼﾞｬﾝﾙNO]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_現金ﾌﾗｸﾞ]  DEFAULT ((0)) FOR [現金ﾌﾗｸﾞ]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_更新時間]  DEFAULT ('') FOR [更新時間]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_IsProcessed]  DEFAULT ((0)) FOR [IsProcessed]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_売掛明細_UpdateAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[SanshiNetSuperSale] ADD  CONSTRAINT [DF_SanshiNetSuperSale_POS区分]  DEFAULT ((1)) FOR [POS区分]
GO



USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[ProcessManager]    Script Date: 2025/08/14 15:27:09 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ProcessManager](
	[Id] [uniqueidentifier] NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗コード] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[WBRT0044出力日時] [datetime] NULL,
	[WBRT0050出力日時] [datetime] NULL,
	[WBRT0042出力日時] [datetime] NULL,
	[WBRT0085出力日時] [datetime] NULL,
	[WBRT0088出力日時] [datetime] NULL,
	[WBRT6008出力日時] [datetime] NULL,
	[Meijidou出力日時] [datetime] NULL,
 CONSTRAINT [PK_ProcessManager] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ProcessManager] ADD  CONSTRAINT [DF_締め処理_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[ProcessManager] ADD  CONSTRAINT [DF_締め処理_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[ProcessManager] ADD  CONSTRAINT [DF_締め処理_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO



USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[ReceiptDetail]    Script Date: 2025/08/14 15:27:34 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ReceiptDetail](
	[Id] [uniqueidentifier] NOT NULL,
	[ReceiptHeaderId] [uniqueidentifier] NOT NULL,
	[SanshiNetSuperSaleId] [uniqueidentifier] NOT NULL,
	[部門コード] [nvarchar](10) NOT NULL,
	[クラスコード] [nvarchar](10) NOT NULL,
	[JANコード] [nvarchar](20) NOT NULL,
	[商品コード] [int] NOT NULL,
	[商品名] [nvarchar](60) NOT NULL,
	[数量] [int] NOT NULL,
	[売価] [int] NOT NULL,
	[原価] [decimal](18, 4) NOT NULL,
	[請求額] [int] NOT NULL,
	[税区分] [int] NOT NULL,
	[税率] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[税抜請求額] [int] NOT NULL,
	[税額] [int] NOT NULL,
	[原価計] [decimal](18, 4) NOT NULL,
	[税込請求額] [int] NOT NULL,
	[売上区分] [int] NULL,
 CONSTRAINT [PK_ReceiptDetail] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_売価]  DEFAULT ((0)) FOR [売価]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_原価]  DEFAULT ((0)) FOR [原価]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_請求額]  DEFAULT ((0)) FOR [請求額]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_税区分]  DEFAULT ((0)) FOR [税区分]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_税率]  DEFAULT ((0)) FOR [税率]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_税抜売価]  DEFAULT ((0)) FOR [税抜請求額]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_税額]  DEFAULT ((0)) FOR [税額]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_原価計]  DEFAULT ((0)) FOR [原価計]
GO

ALTER TABLE [dbo].[ReceiptDetail] ADD  CONSTRAINT [DF_ReceiptDetail_税抜請求額1]  DEFAULT ((0)) FOR [税込請求額]
GO

ALTER TABLE [dbo].[ReceiptDetail]  WITH CHECK ADD  CONSTRAINT [FK_ReceiptDetail_ReceiptHeader] FOREIGN KEY([ReceiptHeaderId])
REFERENCES [dbo].[ReceiptHeader] ([Id])
GO

ALTER TABLE [dbo].[ReceiptDetail] CHECK CONSTRAINT [FK_ReceiptDetail_ReceiptHeader]
GO

ALTER TABLE [dbo].[ReceiptDetail]  WITH CHECK ADD  CONSTRAINT [FK_ReceiptDetail_SanshiNetSuperSale] FOREIGN KEY([SanshiNetSuperSaleId])
REFERENCES [dbo].[SanshiNetSuperSale] ([Id])
GO

ALTER TABLE [dbo].[ReceiptDetail] CHECK CONSTRAINT [FK_ReceiptDetail_SanshiNetSuperSale]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[ReceiptHeader]    Script Date: 2025/08/14 15:27:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ReceiptHeader](
	[Id] [uniqueidentifier] NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗コード] [int] NOT NULL,
	[会員様コード] [int] NOT NULL,
	[売上区分] [int] NULL,
	[レジ番号] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[POS区分] [smallint] NOT NULL,
 CONSTRAINT [PK_ReceiptHeader] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ReceiptHeader] ADD  CONSTRAINT [DF_ReceiptHeader_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[ReceiptHeader] ADD  CONSTRAINT [DF_ReceiptHeader_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[ReceiptHeader] ADD  CONSTRAINT [DF_ReceiptHeader_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[ReceiptHeader] ADD  CONSTRAINT [DF_ReceiptHeader_POS区分]  DEFAULT ((1)) FOR [POS区分]
GO




USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT0042]    Script Date: 2025/08/14 15:28:39 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT0042](
	[Id] [uniqueidentifier] NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗コード] [int] NOT NULL,
	[取引コード] [int] NOT NULL,
	[取引名称] [nvarchar](100) NOT NULL,
	[タイプ] [nvarchar](1) NOT NULL,
	[客数] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[レジNo] [int] NOT NULL,
 CONSTRAINT [PK_WBRT0042] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT0042] ADD  CONSTRAINT [DF_WBRT0042_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT0042] ADD  CONSTRAINT [DF_WBRT0042_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT0042] ADD  CONSTRAINT [DF_WBRT0042_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT0044]    Script Date: 2025/08/14 15:28:51 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT0044](
	[Id] [uniqueidentifier] NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗コード] [int] NOT NULL,
	[部門コード] [nvarchar](50) NOT NULL,
	[売上客数] [int] NOT NULL,
	[売上点数_総売] [int] NOT NULL,
	[売上金額_総売] [int] NOT NULL,
	[概算荒利額_総売] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[返品点数] [int] NOT NULL,
	[返品金額] [int] NOT NULL,
 CONSTRAINT [PK_WBRT0044] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_売上客数]  DEFAULT ((0)) FOR [売上客数]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_売上点数_総売]  DEFAULT ((0)) FOR [売上点数_総売]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_売上金額_総売]  DEFAULT ((0)) FOR [売上金額_総売]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_概算荒利額_総売]  DEFAULT ((0)) FOR [概算荒利額_総売]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  CONSTRAINT [DF_WBRT0044_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  DEFAULT ((0)) FOR [返品点数]
GO

ALTER TABLE [dbo].[WBRT0044] ADD  DEFAULT ((0)) FOR [返品金額]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT0050]    Script Date: 2025/08/14 15:29:02 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT0050](
	[Id] [uniqueidentifier] NOT NULL,
	[配達日] [date] NOT NULL,
	[店舗コード] [int] NOT NULL,
	[クラスコード] [nvarchar](50) NOT NULL,
	[売上客数] [int] NOT NULL,
	[売上点数_総売] [int] NOT NULL,
	[売上金額_総売] [int] NOT NULL,
	[概算荒利額_総売] [int] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
 CONSTRAINT [PK_WBRT0050] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_売上客数]  DEFAULT ((0)) FOR [売上客数]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_売上点数_総売]  DEFAULT ((0)) FOR [売上点数_総売]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_売上金額_総売]  DEFAULT ((0)) FOR [売上金額_総売]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_概算荒利額_総売]  DEFAULT ((0)) FOR [概算荒利額_総売]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT0050] ADD  CONSTRAINT [DF_WBRT0050_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT008x_00_Transaction]    Script Date: 2025/08/14 15:29:13 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT008x_00_Transaction](
	[Id] [uniqueidentifier] NOT NULL,
	[店コード] [int] NOT NULL,
	[ターミナルNo] [int] NOT NULL,
	[取引日付] [date] NOT NULL,
	[営業日] [date] NOT NULL,
	[売上金額] [int] NOT NULL,
	[売上点数] [int] NOT NULL,
	[総売上金額] [int] NOT NULL,
	[営業日日付] [date] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[ReceiptHeaderId] [uniqueidentifier] NOT NULL,
	[返品取引] [int] NOT NULL,
 CONSTRAINT [PK_WBRT008X_0_Transaction] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction] ADD  CONSTRAINT [DF_WBRT008X_Transaction_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction] ADD  CONSTRAINT [DF_WBRT008x_00_Transaction_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction] ADD  CONSTRAINT [DF_WBRT008x_00_Transaction_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction] ADD  DEFAULT ((0)) FOR [返品取引]
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction]  WITH CHECK ADD  CONSTRAINT [FK_WBRT008x_00_Transaction_ReceiptHeader] FOREIGN KEY([ReceiptHeaderId])
REFERENCES [dbo].[ReceiptHeader] ([Id])
GO

ALTER TABLE [dbo].[WBRT008x_00_Transaction] CHECK CONSTRAINT [FK_WBRT008x_00_Transaction_ReceiptHeader]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT008x_01_Item]    Script Date: 2025/08/14 15:29:22 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT008x_01_Item](
	[Id] [uniqueidentifier] NOT NULL,
	[店コード] [int] NOT NULL,
	[ターミナルNo] [int] NOT NULL,
	[取引日付] [date] NOT NULL,
	[営業日] [date] NOT NULL,
	[リンク_クラスコード] [nvarchar](6) NOT NULL,
	[リンク_部門コード] [nvarchar](4) NOT NULL,
	[SKUコード] [nvarchar](16) NOT NULL,
	[商品ANK名称] [nvarchar](28) NOT NULL,
	[商品漢字名称] [nvarchar](28) NOT NULL,
	[単価] [int] NOT NULL,
	[点数] [int] NOT NULL,
	[金額] [int] NOT NULL,
	[原価] [decimal](18, 4) NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[ReceiptHeaderId] [uniqueidentifier] NOT NULL,
	[ReceiptDetailId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_WBRT008X_1_Item] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT008x_01_Item] ADD  CONSTRAINT [DF_WBRT008X_1_Item_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT008x_01_Item] ADD  CONSTRAINT [DF_WBRT008x_01_Item_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_01_Item] ADD  CONSTRAINT [DF_WBRT008x_01_Item_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_01_Item]  WITH CHECK ADD  CONSTRAINT [FK_WBRT008x_01_Item_ReceiptDetail] FOREIGN KEY([ReceiptDetailId])
REFERENCES [dbo].[ReceiptDetail] ([Id])
GO

ALTER TABLE [dbo].[WBRT008x_01_Item] CHECK CONSTRAINT [FK_WBRT008x_01_Item_ReceiptDetail]
GO

ALTER TABLE [dbo].[WBRT008x_01_Item]  WITH CHECK ADD  CONSTRAINT [FK_WBRT008x_01_Item_ReceiptHeader] FOREIGN KEY([ReceiptHeaderId])
REFERENCES [dbo].[ReceiptHeader] ([Id])
GO

ALTER TABLE [dbo].[WBRT008x_01_Item] CHECK CONSTRAINT [FK_WBRT008x_01_Item_ReceiptHeader]
GO


USE [YszNetSuper20250814]
GO

/****** Object:  Table [dbo].[WBRT008x_02_Closing]    Script Date: 2025/08/14 15:29:32 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[WBRT008x_02_Closing](
	[Id] [uniqueidentifier] NOT NULL,
	[店コード] [int] NOT NULL,
	[ターミナルNo] [int] NOT NULL,
	[取引日付] [date] NOT NULL,
	[営業日] [date] NOT NULL,
	[締め金額] [int] NOT NULL,
	[発行日] [date] NOT NULL,
	[営業日日付] [date] NOT NULL,
	[CreatedAt] [datetime] NOT NULL,
	[UpdatedAt] [datetime] NOT NULL,
	[ReceiptHeaderId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_WBRT008X_2_Closing] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[WBRT008x_02_Closing] ADD  CONSTRAINT [DF_WBRT008X_2_Closing_Id]  DEFAULT (newsequentialid()) FOR [Id]
GO

ALTER TABLE [dbo].[WBRT008x_02_Closing] ADD  CONSTRAINT [DF_WBRT008x_02_Closing_CreatedAt]  DEFAULT (getdate()) FOR [CreatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_02_Closing] ADD  CONSTRAINT [DF_WBRT008x_02_Closing_UpdatedAt]  DEFAULT (getdate()) FOR [UpdatedAt]
GO

ALTER TABLE [dbo].[WBRT008x_02_Closing]  WITH CHECK ADD  CONSTRAINT [FK_WBRT008x_02_Closing_ReceiptHeader] FOREIGN KEY([ReceiptHeaderId])
REFERENCES [dbo].[ReceiptHeader] ([Id])
GO

ALTER TABLE [dbo].[WBRT008x_02_Closing] CHECK CONSTRAINT [FK_WBRT008x_02_Closing_ReceiptHeader]
GO


