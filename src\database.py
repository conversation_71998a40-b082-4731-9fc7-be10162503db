"""
データベース接続モジュール
SQL Server接続、トランザクション制御機能を提供
"""

import pyodbc
import logging
from typing import Optional, List, Tuple, Any
from contextlib import contextmanager
from .config import DatabaseConfig


class DatabaseError(Exception):
    """データベースエラー"""
    pass


class DatabaseConnection:
    """データベース接続クラス"""
    
    def __init__(self, config: DatabaseConfig):
        """
        初期化
        
        Args:
            config: データベース設定
        """
        self.config = config
        self.connection: Optional[pyodbc.Connection] = None
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> None:
        """
        データベースに接続
        
        Raises:
            DatabaseError: 接続エラー
        """
        try:
            # 接続文字列の構築
            connection_string = self._build_connection_string()
            
            self.logger.info(f"データベースに接続中: {self.config.host}:{self.config.port}/{self.config.database}")
            
            # 接続の確立
            self.connection = pyodbc.connect(
                connection_string,
                timeout=self.config.login_timeout_sec
            )
            
            # 自動コミットを無効化（トランザクション制御のため）
            self.connection.autocommit = False
            
            self.logger.info("データベース接続が確立されました")
            
        except pyodbc.Error as e:
            error_msg = f"データベース接続エラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
        except Exception as e:
            error_msg = f"予期しないエラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    def disconnect(self) -> None:
        """データベース接続を切断"""
        if self.connection:
            try:
                self.connection.close()
                self.logger.info("データベース接続を切断しました")
            except Exception as e:
                self.logger.warning(f"接続切断時にエラーが発生: {e}")
            finally:
                self.connection = None
    
    def _build_connection_string(self) -> str:
        """接続文字列を構築"""
        parts = [
            f"DRIVER={{{self.config.driver}}}",
            f"SERVER={self.config.host},{self.config.port}",
            f"DATABASE={self.config.database}",
            f"UID={self.config.user}",
            f"PWD={self.config.password}",
        ]
        
        if self.config.encrypt:
            parts.append("Encrypt=yes")
        
        if self.config.trust_server_certificate:
            parts.append("TrustServerCertificate=yes")
        
        return ";".join(parts)
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[Tuple]:
        """
        クエリを実行し、結果を取得
        
        Args:
            query: SQLクエリ
            params: パラメータ
            
        Returns:
            List[Tuple]: クエリ結果
            
        Raises:
            DatabaseError: 実行エラー
        """
        if not self.connection:
            raise DatabaseError("データベースに接続されていません")
        
        try:
            cursor = self.connection.cursor()
            # pyodbcのCursorオブジェクトはtimeout属性をサポートしていない
            # タイムアウトは接続レベルで設定済み

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            results = cursor.fetchall()
            cursor.close()

            return results
            
        except pyodbc.Error as e:
            error_msg = f"クエリ実行エラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    def execute_non_query(self, query: str, params: Optional[Tuple] = None) -> int:
        """
        非選択クエリ（INSERT, UPDATE, DELETE）を実行
        
        Args:
            query: SQLクエリ
            params: パラメータ
            
        Returns:
            int: 影響を受けた行数
            
        Raises:
            DatabaseError: 実行エラー
        """
        if not self.connection:
            raise DatabaseError("データベースに接続されていません")
        
        try:
            cursor = self.connection.cursor()
            # pyodbcのCursorオブジェクトはtimeout属性をサポートしていない
            # タイムアウトは接続レベルで設定済み

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            rowcount = cursor.rowcount
            cursor.close()

            return rowcount
            
        except pyodbc.Error as e:
            error_msg = f"クエリ実行エラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    def commit(self) -> None:
        """トランザクションをコミット"""
        if not self.connection:
            raise DatabaseError("データベースに接続されていません")
        
        try:
            self.connection.commit()
            self.logger.info("トランザクションをコミットしました")
        except pyodbc.Error as e:
            error_msg = f"コミットエラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    def rollback(self) -> None:
        """トランザクションをロールバック"""
        if not self.connection:
            raise DatabaseError("データベースに接続されていません")
        
        try:
            self.connection.rollback()
            self.logger.info("トランザクションをロールバックしました")
        except pyodbc.Error as e:
            error_msg = f"ロールバックエラー: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    @contextmanager
    def transaction(self):
        """
        トランザクションコンテキストマネージャー
        
        使用例:
            with db.transaction():
                db.execute_non_query("DELETE FROM table1 WHERE ...")
                db.execute_non_query("DELETE FROM table2 WHERE ...")
        """
        try:
            yield self
            self.commit()
        except Exception as e:
            self.logger.error(f"トランザクション中にエラーが発生: {e}")
            self.rollback()
            raise
    
    def __enter__(self):
        """コンテキストマネージャーの開始"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """コンテキストマネージャーの終了"""
        if exc_type is not None:
            self.logger.error(f"例外が発生したため、ロールバックします: {exc_val}")
            self.rollback()
        self.disconnect()
