"""
対話式設定入力モジュールのテスト
"""

import unittest
from unittest.mock import Mock, patch
from io import String<PERSON>

from src.interactive_config import InteractiveConfigInput
from src.config import FilterConfig
from src.logger import ApplicationLogger


class TestInteractiveConfigInput(unittest.TestCase):
    """InteractiveConfigInputのテストクラス"""
    
    def setUp(self):
        """テスト前の準備"""
        self.mock_logger = Mock(spec=ApplicationLogger)
        self.interactive_config = InteractiveConfigInput(self.mock_logger)
        
        self.test_filters = FilterConfig(
            store_codes=[100, 200],
            business_date='2025/08/05',
            pos_kbn=None,
            car_no=3
        )
    
    @patch('builtins.input')
    def test_prompt_store_codes_no_change(self, mock_input):
        """店舗コード入力で変更なしのテスト"""
        mock_input.return_value = ''  # Enter キー
        
        result = self.interactive_config._prompt_store_codes([100, 200])
        
        self.assertEqual(result, [100, 200])
    
    @patch('builtins.input')
    def test_prompt_store_codes_valid_input(self, mock_input):
        """店舗コード入力で有効な入力のテスト"""
        mock_input.return_value = '300, 400, 500'
        
        result = self.interactive_config._prompt_store_codes([100, 200])
        
        self.assertEqual(result, [300, 400, 500])
    
    @patch('builtins.input')
    @patch('builtins.print')
    def test_prompt_store_codes_invalid_input(self, mock_print, mock_input):
        """店舗コード入力で無効な入力のテスト"""
        # 最初に無効な入力、次に有効な入力
        mock_input.side_effect = ['10000', '300']  # 10000は範囲外
        
        result = self.interactive_config._prompt_store_codes([100, 200])
        
        self.assertEqual(result, [300])
        # エラーメッセージが表示されることを確認
        mock_print.assert_any_call('   エラー: 店舗コード 10000 は範囲外です (1-9999)')
    
    @patch('builtins.input')
    def test_prompt_business_date_no_change(self, mock_input):
        """営業日入力で変更なしのテスト"""
        mock_input.return_value = ''  # Enter キー
        
        result = self.interactive_config._prompt_business_date('2025/08/05')
        
        self.assertEqual(result, '2025/08/05')
    
    @patch('builtins.input')
    def test_prompt_business_date_valid_input(self, mock_input):
        """営業日入力で有効な入力のテスト"""
        mock_input.return_value = '2025/12/31'
        
        result = self.interactive_config._prompt_business_date('2025/08/05')
        
        self.assertEqual(result, '2025/12/31')
    
    @patch('builtins.input')
    @patch('builtins.print')
    def test_prompt_business_date_invalid_format(self, mock_print, mock_input):
        """営業日入力で無効な形式のテスト"""
        # 最初に無効な形式、次に有効な入力
        mock_input.side_effect = ['2025-08-05', '2025/08/06']
        
        result = self.interactive_config._prompt_business_date('2025/08/05')
        
        self.assertEqual(result, '2025/08/06')
        # エラーメッセージが表示されることを確認
        mock_print.assert_any_call('   エラー: 日付形式が正しくありません (YYYY/MM/DD)')
    
    @patch('builtins.input')
    def test_prompt_pos_kbn_no_change(self, mock_input):
        """POS区分入力で変更なしのテスト"""
        mock_input.return_value = ''  # Enter キー
        
        result = self.interactive_config._prompt_pos_kbn(5)
        
        self.assertEqual(result, 5)
    
    @patch('builtins.input')
    def test_prompt_pos_kbn_set_null(self, mock_input):
        """POS区分入力でnull設定のテスト"""
        mock_input.return_value = 'null'
        
        result = self.interactive_config._prompt_pos_kbn(5)
        
        self.assertIsNone(result)
    
    @patch('builtins.input')
    def test_prompt_pos_kbn_valid_input(self, mock_input):
        """POS区分入力で有効な入力のテスト"""
        mock_input.return_value = '7'
        
        result = self.interactive_config._prompt_pos_kbn(None)
        
        self.assertEqual(result, 7)
    
    @patch('builtins.input')
    def test_prompt_car_no_no_change(self, mock_input):
        """号車番号入力で変更なしのテスト"""
        mock_input.return_value = ''  # Enter キー
        
        result = self.interactive_config._prompt_car_no(3)
        
        self.assertEqual(result, 3)
    
    @patch('builtins.input')
    def test_prompt_car_no_set_null(self, mock_input):
        """号車番号入力でnull設定のテスト"""
        mock_input.return_value = 'none'
        
        result = self.interactive_config._prompt_car_no(3)
        
        self.assertIsNone(result)
    
    @patch('builtins.input')
    def test_prompt_car_no_valid_input(self, mock_input):
        """号車番号入力で有効な入力のテスト"""
        mock_input.return_value = '9'
        
        result = self.interactive_config._prompt_car_no(None)
        
        self.assertEqual(result, 9)
    
    @patch('builtins.input')
    def test_confirm_settings_update_yes(self, mock_input):
        """設定更新確認でYesのテスト"""
        mock_input.return_value = 'Y'
        
        result = self.interactive_config.confirm_settings_update()
        
        self.assertTrue(result)
        self.mock_logger.log_info.assert_called_with("ユーザーが設定更新を承認")
    
    @patch('builtins.input')
    def test_confirm_settings_update_no(self, mock_input):
        """設定更新確認でNoのテスト"""
        mock_input.return_value = 'N'
        
        result = self.interactive_config.confirm_settings_update()
        
        self.assertFalse(result)
        self.mock_logger.log_user_cancelled.assert_called()
    
    @patch('builtins.input')
    def test_confirm_settings_update_keyboard_interrupt(self, mock_input):
        """設定更新確認でキーボード割り込みのテスト"""
        mock_input.side_effect = KeyboardInterrupt()
        
        result = self.interactive_config.confirm_settings_update()
        
        self.assertFalse(result)
        self.mock_logger.log_user_cancelled.assert_called()


if __name__ == '__main__':
    unittest.main()
