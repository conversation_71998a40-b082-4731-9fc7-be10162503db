@echo off
echo ================================
echo Data Cleanup Application Build
echo ================================

echo Checking virtual environment...
if not exist "venv" (
    echo ERROR: Virtual environment not found
    echo Please run setup_en.bat first
    pause
    exit /b 1
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Checking dependencies...
pip install -r requirements.txt

echo Cleaning existing build files...
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "src\__pycache__" rmdir /s /q "src\__pycache__"

echo Creating EXE file...
pyinstaller build.spec

echo Checking build result...
if exist "dist\DataCleanup.exe" (
    echo.
    echo ================================
    echo Build completed successfully!
    echo ================================
    echo EXE file: dist\DataCleanup.exe
    echo.
    echo Usage:
    echo   dist\DataCleanup.exe -c config\cleanup.yml
    echo   dist\DataCleanup.exe -c config\cleanup.yml --dry-run
    echo   dist\DataCleanup.exe -c config\cleanup.yml --interactive
    echo.
) else (
    echo.
    echo ================================
    echo Build failed!
    echo ================================
    echo Please check the error messages above
    echo.
    pause
    exit /b 1
)

echo Deactivating virtual environment...
deactivate

echo Build script completed
pause
