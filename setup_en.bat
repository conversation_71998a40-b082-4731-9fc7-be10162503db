@echo off
echo ================================
echo Data Cleanup Application Setup
echo ================================

echo Step 1: Checking Python...
python --version
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.11 or later
    pause
    exit /b 1
)
echo Python check completed
pause

echo Step 2: Removing existing venv...
if exist "venv" (
    echo Removing existing virtual environment...
    rmdir /s /q venv
)
echo venv removal completed
pause

echo Step 3: Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)
echo Virtual environment created
pause

echo Step 4: Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)
echo Virtual environment activated
pause

echo Step 5: Checking pip...
pip --version
if errorlevel 1 (
    echo ERROR: pip not found
    pause
    exit /b 1
)
echo pip check completed
pause

echo Step 6: Upgrading pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo WARNING: pip upgrade failed
    pause
)
echo pip upgrade completed
pause

echo Step 7: Checking requirements.txt...
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found
    pause
    exit /b 1
)
echo requirements.txt found
pause

echo Step 8: Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo Dependencies installed
pause

echo Step 9: Creating logs directory...
if not exist "logs" mkdir logs
echo Logs directory created
pause

echo Step 10: Deactivating virtual environment...
deactivate
echo Virtual environment deactivated
pause

echo ================================
echo Setup completed successfully!
echo ================================
echo.
echo Next steps:
echo 1. Edit config\cleanup.yml for your environment
echo 2. Run tests: python -m pytest tests/
echo 3. Run application: python -m src.main -c config\cleanup.yml --dry-run
echo 4. Build EXE: build_en.bat
echo.
pause
