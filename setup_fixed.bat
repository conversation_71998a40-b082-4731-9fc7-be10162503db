@echo off
chcp 932 >nul
REM データクリーンアップアプリケーション セットアップスクリプト

echo ================================
echo データクリーンアップアプリケーション
echo 開発環境セットアップスクリプト
echo ================================

REM Pythonのバージョン確認
python --version >nul 2>&1
if errorlevel 1 (
    echo エラー: Pythonがインストールされていません。
    echo Python 3.11以降をインストールしてください。
    pause
    exit /b 1
)

REM Python 3.11以降の確認
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Pythonバージョン: %PYTHON_VERSION%

REM 仮想環境の作成
echo 仮想環境を作成中...
if exist "venv" (
    echo 既存の仮想環境を削除中...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo エラー: 仮想環境の作成に失敗しました。
    pause
    exit /b 1
)

REM 仮想環境をアクティベート
echo 仮想環境をアクティベート中...
call venv\Scripts\activate.bat

REM pipのアップグレード
echo pipをアップグレード中...
python -m pip install --upgrade pip

REM 依存関係のインストール
echo 依存関係をインストール中...
pip install -r requirements.txt
if errorlevel 1 (
    echo エラー: 依存関係のインストールに失敗しました。
    pause
    exit /b 1
)

REM ログディレクトリの作成
echo ログディレクトリを作成中...
if not exist "logs" mkdir logs

echo.
echo ================================
echo セットアップ完了！
echo ================================
echo.
echo 次のステップ:
echo 1. config\cleanup.yml を編集して設定を行ってください
echo 2. テストを実行: python -m pytest tests/
echo 3. アプリケーションを実行: python -m src.main -c config\cleanup.yml --dry-run
echo 4. EXEファイルを作成: build.bat
echo.

REM 仮想環境を非アクティベート
deactivate

echo セットアップスクリプト完了
pause
