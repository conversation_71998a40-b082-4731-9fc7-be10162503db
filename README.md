# データクリーンアップアプリケーション

特定店舗・特定日付・任意の追加条件を基に、再処理の前段として関連テーブルの既存データを安全に削除するためのバッチプログラムです。

## 概要

- **目的**: データ再処理前の既存データ削除
- **対象**: SQL Server データベース
- **実行形態**: 単体EXE（PyInstaller）
- **言語**: Python 3.11以降

## 機能

- 複数テーブルの一括削除処理
- 削除順序の制御（外部キー制約対応）
- ドライランモード（件数確認のみ）
- 対話式削除確認
- ローテーション機能付きログ
- トランザクション制御

## 削除対象テーブル

1. ReceiptDetail
2. ReceiptHeader
3. WBRT008x_01_Item
4. WBRT008x_02_Closing
5. WBRT008x_00_Transaction
6. WBRT0050
7. WBRT0044
8. WBRT0042
9. ProcessManager
10. SanshiNetSuperSale（再取込時のみ）

## セットアップ

### 1. 開発環境のセットアップ

```batch
setup.bat
```

### 2. 設定ファイルの編集

`config/cleanup.yml` を編集して、データベース接続情報と削除条件を設定してください。

```yaml
app:
  log_path: "D:/logs/data_cleanup/%Y%m%d_cleanup.log"
  log_level: "INFO"
  prompt_on_delete: true
  delete_sanshi_only_on_reimport: true

filters:
  store_codes: [100, 200]
  business_date: "2025/08/05"
  pos_kbn: null
  car_no: 3

database:
  driver: "ODBC Driver 18 for SQL Server"
  host: "sqlsvr01.example.local"
  port: 1433
  database: "RetailDB"
  user: "batch_user"
  password: "********"
  encrypt: true
  trust_server_certificate: false
  login_timeout_sec: 30
  command_timeout_sec: 600
```

### 3. EXEファイルの作成

```batch
build.bat
```

## 使用方法

### コマンドライン

```batch
# 通常実行
DataCleanup.exe -c C:\batch\config\cleanup.yml

# ドライラン（件数確認のみ）
DataCleanup.exe -c C:\batch\config\cleanup.yml --dry-run

# 詳細ログ付き実行
DataCleanup.exe -c C:\batch\config\cleanup.yml --verbose
```

### オプション

- `-c, --config`: 設定ファイルパス（必須）
- `--dry-run`: ドライランモード
- `--verbose`: 詳細ログ出力
- `--version`: バージョン表示
- `--help`: ヘルプ表示

## 終了コード

- `0`: 正常終了
- `1`: 異常終了
- `2`: ユーザー中断

## テスト

### 単体テスト実行

```batch
# 仮想環境をアクティベート
venv\Scripts\activate.bat

# テスト実行
python -m pytest tests/ -v

# 仮想環境を非アクティベート
deactivate
```

### 開発モードでの実行

```batch
# 仮想環境をアクティベート
venv\Scripts\activate.bat

# ドライランで動作確認
python -m src.main -c config\cleanup.yml --dry-run

# 仮想環境を非アクティベート
deactivate
```

## ログ

- ログファイル: 設定ファイルで指定したパス
- ローテーション: 10世代、10MB/世代
- 出力内容: 実行条件、件数、削除結果、エラー

## 注意事項

1. **データベース権限**: 対象テーブルへのDELETE権限が必要
2. **バックアップ**: 削除処理は取り消せないため、事前にバックアップを推奨
3. **運用時間**: オンライン処理と競合しないバッチ時間帯での実行を推奨
4. **SanshiNetSuperSale**: 不正データ投入の可能性があるため、再取込時のみ削除

## トラブルシューティング

### よくあるエラー

1. **設定ファイルエラー**
   - YAML形式の確認
   - 必須項目の設定確認
   - 日付形式の確認（YYYY/MM/DD）

2. **データベース接続エラー**
   - 接続情報の確認
   - ODBCドライバーのインストール確認
   - ネットワーク接続の確認

3. **権限エラー**
   - データベースユーザーの権限確認
   - ログファイル出力先の書き込み権限確認

## 開発情報

### プロジェクト構造

```
YszNsTokuRecovery/
├── src/                    # ソースコード
│   ├── main.py            # メインエントリーポイント
│   ├── config.py          # 設定ファイル処理
│   ├── database.py        # データベース接続
│   ├── cleanup_engine.py  # 削除処理エンジン
│   ├── logger.py          # ログ機能
│   ├── interactive.py     # 対話機能
│   └── cli.py             # コマンドライン処理
├── config/                # 設定ファイル
├── tests/                 # テストコード
├── requirements.txt       # 依存関係
├── build.spec            # PyInstaller設定
├── setup.bat             # セットアップスクリプト
└── build.bat             # ビルドスクリプト
```

### 依存関係

- pyodbc: SQL Server接続
- PyYAML: 設定ファイル処理
- pyinstaller: EXE化
- pytest: テスト実行

## ライセンス

社内利用のため、ライセンス情報は省略。

## 更新履歴

- v1.0.0: 初回リリース
