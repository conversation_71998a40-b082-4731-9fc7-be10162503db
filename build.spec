# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller設定ファイル
データクリーンアップアプリケーションのEXE化設定
"""

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# アプリケーションのルートディレクトリ
app_root = os.path.dirname(os.path.abspath(SPEC))

# 追加データファイル
datas = []

# 隠れたインポートを明示的に指定
hiddenimports = [
    'pyodbc',
    'yaml',
    'logging.handlers',
    'datetime',
    'argparse',
    'contextlib',
    'traceback',
    'tempfile',
    'shutil'
]

# PyODBCの依存関係を追加
hiddenimports.extend([
    'pyodbc',
    'decimal',
    'uuid'
])

# YAMLの依存関係を追加
hiddenimports.extend([
    'yaml.loader',
    'yaml.dumper',
    'yaml.constructor',
    'yaml.representer',
    'yaml.resolver'
])

# 除外するモジュール
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'IPython',
    'jupyter'
]

# アナライザー設定
a = Analysis(
    ['src/main.py'],
    pathex=[app_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# PYZ設定（Pythonアーカイブ）
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE設定
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataCleanup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # コンソールアプリケーション
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # アイコンファイルがある場合は指定
    version_file=None,  # バージョン情報ファイルがある場合は指定
)

# Windows用の追加設定
if sys.platform == 'win32':
    # Windows固有の設定があれば追加
    pass
