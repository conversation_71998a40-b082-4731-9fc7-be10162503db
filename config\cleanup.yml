# データクリーンアップアプリケーション設定ファイル
# 仕様書 v1.0 に基づく設定例

# アプリケーション設定
app:
  # ログファイルパス（日付フォーマット対応）
  log_path: "D:/logs/data_cleanup/%Y%m%d_cleanup.log"
  
  # ログレベル（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  log_level: "INFO"
  
  # 削除前の確認プロンプトを表示するかどうか
  prompt_on_delete: true
  
  # SanshiNetSuperSaleの削除を再取込時のみ実行するかどうか
  delete_sanshi_only_on_reimport: true

# フィルター設定
filters:
  # 対象店舗コード（1-9999の整数、最大100件）
  store_codes: [100, 200]
  
  # 営業日（YYYY/MM/DD形式）
  business_date: "2025/08/05"
  
  # POS区分（0-9の整数またはnull）
  pos_kbn: null
  
  # 号車番号（0-9の整数またはnull）
  car_no: 3

# データベース設定
database:
  # ODBCドライバー名
  driver: "ODBC Driver 18 for SQL Server"
  
  # サーバーホスト名またはIPアドレス
  host: "sqlsvr01.example.local"
  
  # ポート番号
  port: 1433
  
  # データベース名
  database: "RetailDB"
  
  # ユーザー名
  user: "batch_user"
  
  # パスワード
  password: "********"
  
  # 暗号化を使用するかどうか
  encrypt: true
  
  # サーバー証明書を信頼するかどうか
  trust_server_certificate: false
  
  # ログインタイムアウト（秒）
  login_timeout_sec: 30
  
  # コマンドタイムアウト（秒）
  command_timeout_sec: 600
