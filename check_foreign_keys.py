"""
外部キー制約確認スクリプト
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import ConfigLoader
from src.database import DatabaseConnection

def check_foreign_keys():
    """外部キー制約を確認"""
    print("=" * 80)
    print("外部キー制約確認")
    print("=" * 80)
    
    try:
        # 設定ファイル読み込み
        config_loader = ConfigLoader()
        config = config_loader.load_config('config/cleanup.yml')
        
        # データベース接続
        db = DatabaseConnection(config.database)
        db.connect()
        
        # 削除対象テーブル
        target_tables = [
            'ReceiptDetail',
            'ReceiptHeader', 
            'WBRT008x_01_Item',
            'WBRT008x_02_Closing',
            'WBRT008x_00_Transaction',
            'WBRT0050',
            'WBRT0044',
            'WBRT0042',
            'ProcessManager',
            'SanshiNetSuperSale'
        ]
        
        print("外部キー制約一覧:")
        print("-" * 80)
        print(f"{'制約名':<40} {'子テーブル':<20} {'親テーブル':<20}")
        print("-" * 80)
        
        # 外部キー制約を取得
        query = """
        SELECT 
            fk.name AS constraint_name,
            OBJECT_NAME(fk.parent_object_id) AS child_table,
            OBJECT_NAME(fk.referenced_object_id) AS parent_table,
            COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS child_column,
            COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS parent_column
        FROM sys.foreign_keys fk
        INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
        WHERE OBJECT_NAME(fk.parent_object_id) IN ({})
           OR OBJECT_NAME(fk.referenced_object_id) IN ({})
        ORDER BY child_table, parent_table
        """.format(
            "'" + "','".join(target_tables) + "'",
            "'" + "','".join(target_tables) + "'"
        )
        
        results = db.execute_query(query)
        
        foreign_keys = {}
        for row in results:
            constraint_name, child_table, parent_table, child_column, parent_column = row
            print(f"{constraint_name:<40} {child_table:<20} {parent_table:<20}")
            
            if child_table not in foreign_keys:
                foreign_keys[child_table] = []
            foreign_keys[child_table].append(parent_table)
        
        print("\n" + "=" * 80)
        print("削除順序の推奨:")
        print("=" * 80)
        
        # 依存関係の分析
        print("各テーブルが参照している親テーブル:")
        for child_table in sorted(foreign_keys.keys()):
            parents = list(set(foreign_keys[child_table]))  # 重複除去
            print(f"{child_table:<25} → {', '.join(parents)}")
        
        print("\n推奨削除順序（子テーブル → 親テーブル）:")
        print("1. 最も多くの親テーブルを参照している子テーブルから削除")
        print("2. 他のテーブルから参照されていないテーブルから削除")
        
        # 参照されているテーブル（親テーブル）の集計
        referenced_tables = {}
        for child_table, parents in foreign_keys.items():
            for parent in parents:
                if parent not in referenced_tables:
                    referenced_tables[parent] = []
                referenced_tables[parent].append(child_table)
        
        print("\n各テーブルを参照している子テーブル:")
        for parent_table in sorted(referenced_tables.keys()):
            children = referenced_tables[parent_table]
            print(f"{parent_table:<25} ← {', '.join(children)}")
        
        db.disconnect()
        
    except Exception as e:
        print(f"エラー: {e}")

if __name__ == "__main__":
    check_foreign_keys()
