"""
メインアプリケーション
データクリーンアップアプリケーションのエントリーポイント
"""

import sys
import traceback
from typing import Optional

from .cli import CommandLineParser, validate_arguments, display_startup_banner, get_exit_code_description
from .config import ConfigLoader, ConfigError
from .database import DatabaseConnection, DatabaseError
from .cleanup_engine import CleanupEngine
from .logger import ApplicationLogger
from .interactive import InteractivePrompt
from .interactive_config import InteractiveConfigInput


class DataCleanupApplication:
    """データクリーンアップアプリケーション"""
    
    def __init__(self):
        """初期化"""
        self.logger: Optional[ApplicationLogger] = None
        self.db: Optional[DatabaseConnection] = None
        self.exit_code = 0
    
    def run(self, args=None) -> int:
        """
        アプリケーションを実行
        
        Args:
            args: コマンドライン引数（テスト用）
            
        Returns:
            int: 終了コード
        """
        try:
            # コマンドライン引数の解析
            parser = CommandLineParser()
            config_path, dry_run, verbose, interactive = parser.parse_args(args)

            # 引数のバリデーション
            validate_arguments(config_path, dry_run, verbose, interactive)

            # 起動バナー表示
            display_startup_banner(config_path, dry_run, interactive)
            
            # 設定ファイルの読み込み
            config_loader = ConfigLoader()
            config = config_loader.load_config(config_path)
            
            # ログ設定の初期化
            self.logger = ApplicationLogger(config.app)
            self.logger.log_startup(config_path)
            
            # 詳細ログモードの設定
            if verbose:
                import logging
                logging.getLogger().setLevel(logging.DEBUG)
                self.logger.log_info("詳細ログモードが有効になりました")
            
            # 対話式設定入力
            if interactive:
                interactive_config = InteractiveConfigInput(self.logger)
                config.filters = interactive_config.prompt_for_filters(config.filters)

                # 設定更新の確認
                if not interactive_config.confirm_settings_update():
                    self.exit_code = 2  # ユーザー中断
                    return self.exit_code
            else:
                # 設定内容のログ出力
                self.logger.log_config_loaded(
                    config.filters.store_codes,
                    config.filters.business_date,
                    config.filters.pos_kbn,
                    config.filters.car_no
                )

            # ドライランモードのログ出力
            if dry_run:
                self.logger.log_dry_run_mode()
            
            # データベース接続
            self.db = DatabaseConnection(config.database)
            self.db.connect()
            self.logger.log_database_connected(config.database.host, config.database.database)
            
            # 削除処理エンジンの初期化
            cleanup_engine = CleanupEngine(self.db, self.logger)
            
            # 対話機能の初期化
            interactive = InteractivePrompt(self.logger)
            
            # SanshiNetSuperSaleを含むかどうかの判定
            include_sanshi = config.app.delete_sanshi_only_on_reimport
            
            # 削除対象件数の集計
            self.logger.log_info("削除対象データの件数を集計中...")
            counts = cleanup_engine.count_target_records(config.filters, include_sanshi)
            
            # 件数サマリーの表示
            total_count = interactive.display_count_summary(counts, include_sanshi)
            
            # エラーがある場合は処理を中断
            if any(count == -1 for count in counts.values()):
                raise Exception("件数集計中にエラーが発生しました")
            
            # 削除確認プロンプト（プロンプト無効化設定の場合はスキップ）
            if config.app.prompt_on_delete:
                if not interactive.confirm_deletion(total_count, dry_run):
                    self.exit_code = 2  # ユーザー中断
                    return self.exit_code
            else:
                self.logger.log_info("プロンプト無効化設定のため、削除確認をスキップします")
                if total_count == 0:
                    self.logger.log_info("削除対象データなしのため処理終了")
                    return 0
            
            # 削除処理の実行
            if total_count > 0:
                self.logger.log_info("削除処理を開始します")
                
                with self.db.transaction():
                    deleted_counts = cleanup_engine.delete_records(
                        config.filters, 
                        include_sanshi, 
                        dry_run
                    )
                    
                    # エラーがある場合は例外を発生
                    if any(count == -1 for count in deleted_counts.values()):
                        raise Exception("削除処理中にエラーが発生しました")
                    
                    if not dry_run:
                        self.logger.log_transaction_commit()
                
                # 完了サマリーの表示
                interactive.display_completion_summary(deleted_counts, dry_run)
            
            self.exit_code = 0
            return self.exit_code
            
        except KeyboardInterrupt:
            error_msg = "ユーザーによって処理が中断されました"
            if self.logger:
                self.logger.log_user_cancelled()
            else:
                print(f"\nエラー: {error_msg}")
            self.exit_code = 2
            return self.exit_code
            
        except ConfigError as e:
            error_msg = f"設定エラー: {e}"
            if self.logger:
                self.logger.log_error(error_msg)
            else:
                print(f"エラー: {error_msg}")
            self.exit_code = 1
            return self.exit_code
            
        except DatabaseError as e:
            error_msg = f"データベースエラー: {e}"
            if self.logger:
                self.logger.log_error(error_msg)
                if self.db:
                    try:
                        self.db.rollback()
                        self.logger.log_transaction_rollback(str(e))
                    except:
                        pass
            else:
                print(f"エラー: {error_msg}")
            self.exit_code = 1
            return self.exit_code
            
        except Exception as e:
            error_msg = f"予期しないエラー: {e}"
            if self.logger:
                self.logger.log_error(error_msg)
                self.logger.log_debug(f"スタックトレース: {traceback.format_exc()}")
                if self.db:
                    try:
                        self.db.rollback()
                        self.logger.log_transaction_rollback(str(e))
                    except:
                        pass
            else:
                print(f"エラー: {error_msg}")
                print(f"詳細: {traceback.format_exc()}")
            self.exit_code = 1
            return self.exit_code
            
        finally:
            # リソースのクリーンアップ
            if self.db:
                self.db.disconnect()
            
            if self.logger:
                self.logger.log_shutdown(self.exit_code)
                print(f"\n処理が完了しました。終了コード: {self.exit_code} ({get_exit_code_description(self.exit_code)})")


def main():
    """メイン関数"""
    app = DataCleanupApplication()
    exit_code = app.run()
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
