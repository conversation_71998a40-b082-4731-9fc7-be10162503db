@echo off
chcp 932 >nul
REM データクリーンアップアプリケーション ビルドスクリプト

echo ================================
echo データクリーンアップアプリケーション
echo EXEビルドスクリプト
echo ================================

REM 仮想環境の確認
if not exist "venv" (
    echo 仮想環境が見つかりません。setup_fixed.batを先に実行してください。
    pause
    exit /b 1
)

REM 仮想環境をアクティベート
call venv\Scripts\activate.bat

REM 依存関係のインストール確認
echo 依存関係を確認中...
pip install -r requirements.txt

REM 既存のビルドファイルをクリーンアップ
echo 既存のビルドファイルをクリーンアップ中...
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "src\__pycache__" rmdir /s /q "src\__pycache__"

REM PyInstallerでEXEを作成
echo EXEファイルを作成中...
pyinstaller build.spec

REM ビルド結果の確認
if exist "dist\DataCleanup.exe" (
    echo.
    echo ================================
    echo ビルド完了！
    echo ================================
    echo EXEファイル: dist\DataCleanup.exe
    echo.
    echo 使用方法:
    echo   dist\DataCleanup.exe -c config\cleanup.yml
    echo   dist\DataCleanup.exe -c config\cleanup.yml --dry-run
    echo.
) else (
    echo.
    echo ================================
    echo ビルドエラー
    echo ================================
    echo EXEファイルの作成に失敗しました。
    echo ログを確認してください。
    echo.
    pause
    exit /b 1
)

REM 仮想環境を非アクティベート
deactivate

echo ビルドスクリプト完了
pause
