"""
対話式設定機能のテストスクリプト
"""

import subprocess
import sys
import time
from io import StringIO

def test_interactive_mode():
    """対話式モードのテスト"""
    print("=" * 60)
    print("対話式設定機能のテスト")
    print("=" * 60)
    
    # テスト入力データ
    test_inputs = [
        "500,600,700",      # 店舗コード
        "2025/12/31",       # 営業日
        "5",                # POS区分
        "9",                # 号車番号
        "Y"                 # 実行確認
    ]
    
    # 入力データを改行で結合
    input_data = "\n".join(test_inputs) + "\n"
    
    try:
        # プロセスを起動
        process = subprocess.Popen(
            [sys.executable, "-m", "src.main", "-c", "config/cleanup.yml", "--interactive", "--dry-run"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="."
        )
        
        # 入力データを送信して結果を取得
        stdout, stderr = process.communicate(input=input_data, timeout=30)
        
        print("標準出力:")
        print(stdout)
        print("\n標準エラー出力:")
        print(stderr)
        print(f"\n終了コード: {process.returncode}")
        
        return process.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("タイムアウトが発生しました")
        process.kill()
        return False
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        return False

def test_interactive_cancel():
    """対話式モードのキャンセルテスト"""
    print("\n" + "=" * 60)
    print("対話式設定機能のキャンセルテスト")
    print("=" * 60)
    
    # テスト入力データ（最後にNでキャンセル）
    test_inputs = [
        "800,900",          # 店舗コード
        "",                 # 営業日（変更なし）
        "null",             # POS区分（null）
        "null",             # 号車番号（null）
        "N"                 # 実行キャンセル
    ]
    
    # 入力データを改行で結合
    input_data = "\n".join(test_inputs) + "\n"
    
    try:
        # プロセスを起動
        process = subprocess.Popen(
            [sys.executable, "-m", "src.main", "-c", "config/cleanup.yml", "--interactive", "--dry-run"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="."
        )
        
        # 入力データを送信して結果を取得
        stdout, stderr = process.communicate(input=input_data, timeout=30)
        
        print("標準出力:")
        print(stdout)
        print("\n標準エラー出力:")
        print(stderr)
        print(f"\n終了コード: {process.returncode}")
        
        # キャンセルの場合は終了コード2が期待される
        return process.returncode == 2
        
    except subprocess.TimeoutExpired:
        print("タイムアウトが発生しました")
        process.kill()
        return False
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        return False

def test_normal_mode():
    """通常モードのテスト（比較用）"""
    print("\n" + "=" * 60)
    print("通常モード（非対話式）のテスト")
    print("=" * 60)
    
    try:
        # プロセスを起動
        process = subprocess.Popen(
            [sys.executable, "-m", "src.main", "-c", "config/cleanup.yml", "--dry-run"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="."
        )
        
        # 結果を取得
        stdout, stderr = process.communicate(timeout=30)
        
        print("標準出力:")
        print(stdout)
        print("\n標準エラー出力:")
        print(stderr)
        print(f"\n終了コード: {process.returncode}")
        
        return process.returncode == 2  # データなしの場合は終了コード2
        
    except subprocess.TimeoutExpired:
        print("タイムアウトが発生しました")
        process.kill()
        return False
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        return False

if __name__ == "__main__":
    print("データクリーンアップアプリケーション - 対話式テスト")
    print("=" * 60)
    
    # テスト実行
    test1_result = test_interactive_mode()
    test2_result = test_interactive_cancel()
    test3_result = test_normal_mode()
    
    # 結果サマリー
    print("\n" + "=" * 60)
    print("テスト結果サマリー")
    print("=" * 60)
    print(f"対話式設定テスト: {'✅ 成功' if test1_result else '❌ 失敗'}")
    print(f"対話式キャンセルテスト: {'✅ 成功' if test2_result else '❌ 失敗'}")
    print(f"通常モードテスト: {'✅ 成功' if test3_result else '❌ 失敗'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 全てのテストが成功しました！")
        sys.exit(0)
    else:
        print("\n⚠️ 一部のテストが失敗しました。")
        sys.exit(1)
