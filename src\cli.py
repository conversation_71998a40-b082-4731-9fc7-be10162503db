"""
コマンドライン引数処理モジュール
argparseを使用したコマンドライン引数処理とドライラン機能を提供
"""

import argparse
import sys
from typing import Tuple


class CommandLineParser:
    """コマンドライン引数パーサー"""
    
    def __init__(self):
        """初期化"""
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """引数パーサーを作成"""
        parser = argparse.ArgumentParser(
            prog='DataCleanup',
            description='データ再処理用バッチ（削除処理）',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用例:
  DataCleanup.exe -c C:\\batch\\config\\cleanup.yml
  DataCleanup.exe -c C:\\batch\\config\\cleanup.yml --dry-run
  DataCleanup.exe -c C:\\batch\\config\\cleanup.yml --interactive

終了コード:
  0: 正常終了
  1: 異常終了
  2: ユーザー中断
            """
        )
        
        # 必須引数: 設定ファイルパス
        parser.add_argument(
            '-c', '--config',
            required=True,
            metavar='CONFIG_FILE',
            help='設定ファイルのパス（YAML形式）'
        )
        
        # オプション引数: ドライランモード
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='ドライランモード（件数確認のみ、実削除は行わない）'
        )
        
        # オプション引数: バージョン表示
        parser.add_argument(
            '--version',
            action='version',
            version='%(prog)s 1.0.0'
        )
        
        # オプション引数: 詳細ログ
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='詳細ログを出力'
        )

        # オプション引数: 対話式設定入力
        parser.add_argument(
            '--interactive',
            action='store_true',
            help='対話式で設定を入力（設定ファイルの値を上書き）'
        )
        
        return parser
    
    def parse_args(self, args=None) -> Tuple[str, bool, bool, bool]:
        """
        コマンドライン引数を解析

        Args:
            args: 引数リスト（Noneの場合はsys.argvを使用）

        Returns:
            Tuple[str, bool, bool, bool]: (設定ファイルパス, ドライランモード, 詳細ログモード, 対話式モード)
        """
        try:
            parsed_args = self.parser.parse_args(args)
            return (
                parsed_args.config,
                parsed_args.dry_run,
                parsed_args.verbose,
                parsed_args.interactive
            )
        except SystemExit as e:
            # argparseがヘルプ表示やエラーで終了する場合
            if e.code == 0:
                # ヘルプ表示の場合は正常終了
                sys.exit(0)
            else:
                # エラーの場合は異常終了
                sys.exit(1)
    
    def print_help(self) -> None:
        """ヘルプを表示"""
        self.parser.print_help()


def validate_arguments(config_path: str, dry_run: bool, verbose: bool, interactive: bool) -> None:
    """
    引数の追加バリデーション

    Args:
        config_path: 設定ファイルパス
        dry_run: ドライランモード
        verbose: 詳細ログモード
        interactive: 対話式モード

    Raises:
        ValueError: バリデーションエラー
    """
    import os
    
    # 設定ファイルの存在確認
    if not os.path.exists(config_path):
        raise ValueError(f"設定ファイルが見つかりません: {config_path}")
    
    # 設定ファイルの拡張子確認
    if not config_path.lower().endswith(('.yml', '.yaml')):
        raise ValueError(f"設定ファイルはYAML形式である必要があります: {config_path}")
    
    # 設定ファイルの読み取り権限確認
    if not os.access(config_path, os.R_OK):
        raise ValueError(f"設定ファイルの読み取り権限がありません: {config_path}")


def display_startup_banner(config_path: str, dry_run: bool, interactive: bool = False) -> None:
    """
    起動バナーを表示

    Args:
        config_path: 設定ファイルパス
        dry_run: ドライランモード
        interactive: 対話式モード
    """
    print("=" * 60)
    print("データクリーンアップアプリケーション v1.0.0")
    print("=" * 60)
    print(f"設定ファイル: {config_path}")
    if interactive:
        print("モード: 対話式設定入力")
    elif dry_run:
        print("モード: ドライラン（実削除は行いません）")
    else:
        print("モード: 通常実行")
    print("=" * 60)
    print()


def get_exit_code_description(exit_code: int) -> str:
    """
    終了コードの説明を取得
    
    Args:
        exit_code: 終了コード
        
    Returns:
        str: 終了コードの説明
    """
    descriptions = {
        0: "正常終了",
        1: "異常終了",
        2: "ユーザー中断"
    }
    return descriptions.get(exit_code, f"不明な終了コード: {exit_code}")
