"""
設定ファイル処理モジュール
YAML設定ファイルの読み込み、バリデーション機能を提供
"""

import os
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class AppConfig:
    """アプリケーション設定"""
    log_path: str
    log_level: str
    prompt_on_delete: bool
    delete_sanshi_only_on_reimport: bool


@dataclass
class FilterConfig:
    """フィルター設定"""
    store_codes: List[int]
    business_date: str
    pos_kbn: Optional[int]
    car_no: Optional[int]


@dataclass
class DatabaseConfig:
    """データベース設定"""
    driver: str
    host: str
    port: int
    database: str
    user: str
    password: str
    encrypt: bool
    trust_server_certificate: bool
    login_timeout_sec: int
    command_timeout_sec: int


@dataclass
class Config:
    """全体設定"""
    app: AppConfig
    filters: FilterConfig
    database: DatabaseConfig


class ConfigError(Exception):
    """設定エラー"""
    pass


class ConfigLoader:
    """設定ファイルローダー"""
    
    def __init__(self):
        pass
    
    def load_config(self, config_path: str) -> Config:
        """
        設定ファイルを読み込み、バリデーションを実行
        
        Args:
            config_path: 設定ファイルパス
            
        Returns:
            Config: 設定オブジェクト
            
        Raises:
            ConfigError: 設定エラー
        """
        if not os.path.exists(config_path):
            raise ConfigError(f"設定ファイルが見つかりません: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ConfigError(f"YAML解析エラー: {e}")
        except Exception as e:
            raise ConfigError(f"設定ファイル読み込みエラー: {e}")
        
        return self._validate_and_create_config(config_data)
    
    def _validate_and_create_config(self, config_data: Dict[str, Any]) -> Config:
        """
        設定データのバリデーションと設定オブジェクト作成
        
        Args:
            config_data: 設定データ辞書
            
        Returns:
            Config: 設定オブジェクト
            
        Raises:
            ConfigError: バリデーションエラー
        """
        # 必須セクションの確認
        required_sections = ['app', 'filters', 'database']
        for section in required_sections:
            if section not in config_data:
                raise ConfigError(f"必須セクション '{section}' が見つかりません")
        
        # アプリケーション設定のバリデーション
        app_config = self._validate_app_config(config_data['app'])
        
        # フィルター設定のバリデーション
        filter_config = self._validate_filter_config(config_data['filters'])
        
        # データベース設定のバリデーション
        db_config = self._validate_database_config(config_data['database'])
        
        return Config(
            app=app_config,
            filters=filter_config,
            database=db_config
        )
    
    def _validate_app_config(self, app_data: Dict[str, Any]) -> AppConfig:
        """アプリケーション設定のバリデーション"""
        required_fields = ['log_path', 'log_level', 'prompt_on_delete', 'delete_sanshi_only_on_reimport']
        for field in required_fields:
            if field not in app_data:
                raise ConfigError(f"app.{field} が設定されていません")
        
        # ログレベルの確認
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if app_data['log_level'] not in valid_log_levels:
            raise ConfigError(f"無効なログレベル: {app_data['log_level']}")
        
        return AppConfig(
            log_path=str(app_data['log_path']),
            log_level=str(app_data['log_level']),
            prompt_on_delete=bool(app_data['prompt_on_delete']),
            delete_sanshi_only_on_reimport=bool(app_data['delete_sanshi_only_on_reimport'])
        )
    
    def _validate_filter_config(self, filter_data: Dict[str, Any]) -> FilterConfig:
        """フィルター設定のバリデーション"""
        required_fields = ['store_codes', 'business_date']
        for field in required_fields:
            if field not in filter_data:
                raise ConfigError(f"filters.{field} が設定されていません")
        
        # 店舗コードのバリデーション
        store_codes = filter_data['store_codes']
        if not isinstance(store_codes, list) or len(store_codes) == 0:
            raise ConfigError("store_codes は空でない配列である必要があります")
        
        if len(store_codes) > 100:
            raise ConfigError("store_codes の最大件数は100です")
        
        for code in store_codes:
            if not isinstance(code, int) or code < 1 or code > 9999:
                raise ConfigError(f"無効な店舗コード: {code} (1-9999の整数である必要があります)")
        
        # 営業日のバリデーション
        business_date = filter_data['business_date']
        try:
            datetime.strptime(business_date, '%Y/%m/%d')
        except ValueError:
            raise ConfigError(f"無効な日付形式: {business_date} (YYYY/MM/DD形式である必要があります)")
        
        # POS区分のバリデーション
        pos_kbn = filter_data.get('pos_kbn')
        if pos_kbn is not None:
            if not isinstance(pos_kbn, int) or pos_kbn < 0 or pos_kbn > 9:
                raise ConfigError(f"無効なPOS区分: {pos_kbn} (0-9の整数またはnullである必要があります)")
        
        # 号車番号のバリデーション
        car_no = filter_data.get('car_no')
        if car_no is not None:
            if not isinstance(car_no, int) or car_no < 0 or car_no > 9:
                raise ConfigError(f"無効な号車番号: {car_no} (0-9の整数またはnullである必要があります)")
        
        return FilterConfig(
            store_codes=store_codes,
            business_date=business_date,
            pos_kbn=pos_kbn,
            car_no=car_no
        )
    
    def _validate_database_config(self, db_data: Dict[str, Any]) -> DatabaseConfig:
        """データベース設定のバリデーション"""
        required_fields = ['driver', 'host', 'port', 'database', 'user', 'password']
        for field in required_fields:
            if field not in db_data:
                raise ConfigError(f"database.{field} が設定されていません")
        
        # ポート番号のバリデーション
        port = db_data['port']
        if not isinstance(port, int) or port < 1 or port > 65535:
            raise ConfigError(f"無効なポート番号: {port}")
        
        # タイムアウト値のバリデーション
        login_timeout = db_data.get('login_timeout_sec', 30)
        command_timeout = db_data.get('command_timeout_sec', 600)
        
        if not isinstance(login_timeout, int) or login_timeout < 1:
            raise ConfigError(f"無効なログインタイムアウト: {login_timeout}")
        
        if not isinstance(command_timeout, int) or command_timeout < 1:
            raise ConfigError(f"無効なコマンドタイムアウト: {command_timeout}")
        
        return DatabaseConfig(
            driver=str(db_data['driver']),
            host=str(db_data['host']),
            port=port,
            database=str(db_data['database']),
            user=str(db_data['user']),
            password=str(db_data['password']),
            encrypt=bool(db_data.get('encrypt', True)),
            trust_server_certificate=bool(db_data.get('trust_server_certificate', False)),
            login_timeout_sec=login_timeout,
            command_timeout_sec=command_timeout
        )
