"""
設定ファイル処理モジュールのテスト
"""

import unittest
import tempfile
import os
from unittest.mock import patch
import yaml

from src.config import Config<PERSON>oa<PERSON>, ConfigError, Config, AppConfig, FilterConfig, DatabaseConfig


class TestConfigLoader(unittest.TestCase):
    """ConfigLoaderのテストクラス"""
    
    def setUp(self):
        """テスト前の準備"""
        self.config_loader = ConfigLoader()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """テスト後のクリーンアップ"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_temp_config(self, config_data):
        """一時的な設定ファイルを作成"""
        config_path = os.path.join(self.temp_dir, 'test_config.yml')
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        return config_path
    
    def test_load_valid_config(self):
        """正常な設定ファイルの読み込みテスト"""
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INFO',
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            },
            'filters': {
                'store_codes': [100, 200],
                'business_date': '2025/08/05',
                'pos_kbn': None,
                'car_no': 3
            },
            'database': {
                'driver': 'ODBC Driver 18 for SQL Server',
                'host': 'localhost',
                'port': 1433,
                'database': 'TestDB',
                'user': 'test_user',
                'password': 'test_pass',
                'encrypt': True,
                'trust_server_certificate': False,
                'login_timeout_sec': 30,
                'command_timeout_sec': 600
            }
        }
        
        config_path = self.create_temp_config(config_data)
        config = self.config_loader.load_config(config_path)
        
        self.assertIsInstance(config, Config)
        self.assertEqual(config.app.log_level, 'INFO')
        self.assertEqual(config.filters.store_codes, [100, 200])
        self.assertEqual(config.database.host, 'localhost')
    
    def test_load_nonexistent_file(self):
        """存在しないファイルの読み込みテスト"""
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config('nonexistent.yml')
        
        self.assertIn('設定ファイルが見つかりません', str(cm.exception))
    
    def test_invalid_yaml(self):
        """無効なYAMLファイルのテスト"""
        config_path = os.path.join(self.temp_dir, 'invalid.yml')
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write('invalid: yaml: content: [')
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('YAML解析エラー', str(cm.exception))
    
    def test_missing_required_section(self):
        """必須セクション不足のテスト"""
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INFO',
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            }
            # filters, database セクションが不足
        }
        
        config_path = self.create_temp_config(config_data)
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('必須セクション', str(cm.exception))
    
    def test_invalid_log_level(self):
        """無効なログレベルのテスト"""
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INVALID',  # 無効なログレベル
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            },
            'filters': {
                'store_codes': [100],
                'business_date': '2025/08/05'
            },
            'database': {
                'driver': 'test',
                'host': 'localhost',
                'port': 1433,
                'database': 'test',
                'user': 'test',
                'password': 'test'
            }
        }
        
        config_path = self.create_temp_config(config_data)
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('無効なログレベル', str(cm.exception))
    
    def test_invalid_store_codes(self):
        """無効な店舗コードのテスト"""
        # 空の配列
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INFO',
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            },
            'filters': {
                'store_codes': [],  # 空の配列
                'business_date': '2025/08/05'
            },
            'database': {
                'driver': 'test',
                'host': 'localhost',
                'port': 1433,
                'database': 'test',
                'user': 'test',
                'password': 'test'
            }
        }
        
        config_path = self.create_temp_config(config_data)
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('空でない配列', str(cm.exception))
    
    def test_invalid_business_date(self):
        """無効な営業日のテスト"""
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INFO',
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            },
            'filters': {
                'store_codes': [100],
                'business_date': '2025-08-05'  # 無効な日付形式
            },
            'database': {
                'driver': 'test',
                'host': 'localhost',
                'port': 1433,
                'database': 'test',
                'user': 'test',
                'password': 'test'
            }
        }
        
        config_path = self.create_temp_config(config_data)
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('無効な日付形式', str(cm.exception))
    
    def test_invalid_port(self):
        """無効なポート番号のテスト"""
        config_data = {
            'app': {
                'log_path': 'test.log',
                'log_level': 'INFO',
                'prompt_on_delete': True,
                'delete_sanshi_only_on_reimport': True
            },
            'filters': {
                'store_codes': [100],
                'business_date': '2025/08/05'
            },
            'database': {
                'driver': 'test',
                'host': 'localhost',
                'port': 99999,  # 無効なポート番号
                'database': 'test',
                'user': 'test',
                'password': 'test'
            }
        }
        
        config_path = self.create_temp_config(config_data)
        
        with self.assertRaises(ConfigError) as cm:
            self.config_loader.load_config(config_path)
        
        self.assertIn('無効なポート番号', str(cm.exception))


if __name__ == '__main__':
    unittest.main()
