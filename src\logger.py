"""
ログ機能モジュール
ローテーション機能付きログシステムを提供
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
from typing import Optional
from src.config import AppConfig


class LoggerSetup:
    """ログ設定クラス"""
    
    @staticmethod
    def setup_logger(config: AppConfig, logger_name: Optional[str] = None) -> logging.Logger:
        """
        ログ設定を初期化
        
        Args:
            config: アプリケーション設定
            logger_name: ロガー名（Noneの場合はルートロガー）
            
        Returns:
            logging.Logger: 設定済みロガー
        """
        # ロガーの取得
        logger = logging.getLogger(logger_name)
        
        # 既存のハンドラーをクリア
        logger.handlers.clear()
        
        # ログレベルの設定
        log_level = getattr(logging, config.log_level.upper())
        logger.setLevel(log_level)
        
        # ログファイルパスの処理（日付フォーマット対応）
        log_path = datetime.now().strftime(config.log_path)
        
        # ログディレクトリの作成
        log_dir = os.path.dirname(log_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # ファイルハンドラーの設定（ローテーション機能付き）
        file_handler = RotatingFileHandler(
            log_path,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,  # 10世代
            encoding='utf-8'
        )
        
        # コンソールハンドラーの設定
        console_handler = logging.StreamHandler()
        
        # フォーマッターの設定
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # ハンドラーをロガーに追加
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        # 親ロガーへの伝播を無効化（重複ログ防止）
        logger.propagate = False
        
        return logger


class ApplicationLogger:
    """アプリケーション専用ログクラス"""
    
    def __init__(self, config: AppConfig):
        """
        初期化
        
        Args:
            config: アプリケーション設定
        """
        self.logger = LoggerSetup.setup_logger(config, 'DataCleanup')
    
    def log_startup(self, config_path: str) -> None:
        """アプリケーション開始ログ"""
        self.logger.info("=" * 60)
        self.logger.info("データクリーンアップアプリケーション開始")
        self.logger.info(f"設定ファイル: {config_path}")
        self.logger.info("=" * 60)
    
    def log_shutdown(self, exit_code: int) -> None:
        """アプリケーション終了ログ"""
        self.logger.info("=" * 60)
        self.logger.info(f"データクリーンアップアプリケーション終了 (終了コード: {exit_code})")
        self.logger.info("=" * 60)
    
    def log_config_loaded(self, store_codes: list, business_date: str, pos_kbn: Optional[int], car_no: Optional[int]) -> None:
        """設定読み込み完了ログ"""
        self.logger.info("設定ファイル読み込み完了")
        self.logger.info(f"対象店舗: {store_codes}")
        self.logger.info(f"営業日: {business_date}")
        if pos_kbn is not None:
            self.logger.info(f"POS区分: {pos_kbn}")
        if car_no is not None:
            self.logger.info(f"号車番号: {car_no}")
    
    def log_database_connected(self, host: str, database: str) -> None:
        """データベース接続完了ログ"""
        self.logger.info(f"データベース接続完了: {host}/{database}")
    
    def log_count_result(self, table_name: str, count: int) -> None:
        """件数集計結果ログ"""
        self.logger.info(f"[件数確認] {table_name}: {count:,} 件")
    
    def log_delete_start(self, table_name: str) -> None:
        """削除開始ログ"""
        self.logger.info(f"[削除開始] {table_name}")
    
    def log_delete_result(self, table_name: str, deleted_count: int) -> None:
        """削除結果ログ"""
        self.logger.info(f"[削除完了] {table_name}: {deleted_count:,} 件削除")
    
    def log_delete_skipped(self, table_name: str) -> None:
        """削除スキップログ"""
        self.logger.info(f"[削除スキップ] {table_name}: 対象データなし")
    
    def log_dry_run_mode(self) -> None:
        """ドライランモードログ"""
        self.logger.info("*** ドライランモード: 実際の削除は行いません ***")
    
    def log_transaction_commit(self) -> None:
        """トランザクションコミットログ"""
        self.logger.info("全ての削除処理が完了し、トランザクションをコミットしました")
    
    def log_transaction_rollback(self, error: str) -> None:
        """トランザクションロールバックログ"""
        self.logger.error(f"エラーが発生したため、トランザクションをロールバックしました: {error}")
    
    def log_user_cancelled(self) -> None:
        """ユーザーキャンセルログ"""
        self.logger.info("ユーザーによって処理がキャンセルされました")
    
    def log_error(self, error: str) -> None:
        """エラーログ"""
        self.logger.error(error)
    
    def log_warning(self, warning: str) -> None:
        """警告ログ"""
        self.logger.warning(warning)
    
    def log_info(self, info: str) -> None:
        """情報ログ"""
        self.logger.info(info)
    
    def log_debug(self, debug: str) -> None:
        """デバッグログ"""
        self.logger.debug(debug)
