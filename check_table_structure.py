"""
テーブル構造確認スクリプト
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import ConfigLoader
from src.database import DatabaseConnection

def check_table_structure():
    """テーブル構造を確認"""
    print("=" * 60)
    print("テーブル構造確認")
    print("=" * 60)
    
    try:
        # 設定ファイル読み込み
        config_loader = ConfigLoader()
        config = config_loader.load_config('config/cleanup.yml')
        
        # データベース接続
        db = DatabaseConnection(config.database)
        db.connect()
        
        # 確認対象テーブル
        tables_to_check = [
            'WBRT008x_01_Item',
            'WBRT008x_02_Closing', 
            'WBRT008x_00_Transaction',
            'SanshiNetSuperSale'
        ]
        
        for table_name in tables_to_check:
            print(f"\n--- {table_name} ---")
            try:
                # テーブルの列情報を取得
                query = f"""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
                """
                
                results = db.execute_query(query)
                
                if results:
                    print("列名                    データ型        NULL許可")
                    print("-" * 50)
                    for row in results:
                        column_name, data_type, is_nullable = row
                        print(f"{column_name:<20} {data_type:<12} {is_nullable}")
                else:
                    print("テーブルが存在しないか、列情報を取得できませんでした")
                    
            except Exception as e:
                print(f"エラー: {e}")
        
        db.disconnect()
        
    except Exception as e:
        print(f"全体エラー: {e}")

if __name__ == "__main__":
    check_table_structure()
