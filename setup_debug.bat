@echo off
echo ===== デバッグ用セットアップスクリプト =====
pause

echo Step 1: Python確認中...
python --version
if errorlevel 1 (
    echo エラー: Pythonが見つかりません
    pause
    exit /b 1
)
echo Python確認完了
pause

echo Step 2: 既存venv削除中...
if exist "venv" (
    echo 既存の仮想環境を削除中...
    rmdir /s /q venv
    if errorlevel 1 (
        echo 警告: venv削除でエラーが発生しました
        pause
    )
)
echo venv削除完了
pause

echo Step 3: 仮想環境作成中...
python -m venv venv
if errorlevel 1 (
    echo エラー: 仮想環境作成に失敗しました
    pause
    exit /b 1
)
echo 仮想環境作成完了
pause

echo Step 4: 仮想環境アクティベート中...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo エラー: 仮想環境のアクティベートに失敗しました
    pause
    exit /b 1
)
echo 仮想環境アクティベート完了
pause

echo Step 5: pip確認中...
pip --version
if errorlevel 1 (
    echo エラー: pipが見つかりません
    pause
    exit /b 1
)
echo pip確認完了
pause

echo Step 6: pipアップグレード中...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo 警告: pipアップグレードでエラーが発生しました
    pause
)
echo pipアップグレード完了
pause

echo Step 7: requirements.txt確認中...
if not exist "requirements.txt" (
    echo エラー: requirements.txtが見つかりません
    pause
    exit /b 1
)
type requirements.txt
echo requirements.txt確認完了
pause

echo Step 8: 依存関係インストール中...
pip install -r requirements.txt
if errorlevel 1 (
    echo エラー: 依存関係のインストールに失敗しました
    pause
    exit /b 1
)
echo 依存関係インストール完了
pause

echo Step 9: ログディレクトリ作成中...
if not exist "logs" mkdir logs
echo ログディレクトリ作成完了
pause

echo Step 10: 仮想環境非アクティベート中...
deactivate
echo 仮想環境非アクティベート完了
pause

echo ===== セットアップ完了 =====
echo 全ての処理が正常に完了しました
pause
