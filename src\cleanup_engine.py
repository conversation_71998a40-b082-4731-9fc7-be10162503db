"""
削除処理エンジンモジュール
各テーブルの削除処理、件数集計、削除順序制御を提供
"""

from typing import Dict, List, Tuple, Optional
from datetime import datetime
from .config import FilterConfig
from .database import DatabaseConnection
from .logger import ApplicationLogger


class CleanupEngine:
    """削除処理エンジン"""
    
    # 削除順序（仕様書に基づく）
    DELETE_ORDER = [
        'ReceiptDetail',
        'ReceiptHeader', 
        'WBRT008x_01_Item',
        'WBRT008x_02_Closing',
        'WBRT008x_00_Transaction',
        'WBRT0050',
        'WBRT0044',
        'WBRT0042',
        'ProcessManager',
        'SanshiNetSuperSale'  # 再取込時のみ
    ]
    
    def __init__(self, db: DatabaseConnection, logger: ApplicationLogger):
        """
        初期化
        
        Args:
            db: データベース接続
            logger: ログ
        """
        self.db = db
        self.logger = logger
    
    def count_target_records(self, filters: FilterConfig, include_sanshi: bool = False) -> Dict[str, int]:
        """
        削除対象レコード数を集計
        
        Args:
            filters: フィルター設定
            include_sanshi: SanshiNetSuperSaleを含むかどうか
            
        Returns:
            Dict[str, int]: テーブル名と件数の辞書
        """
        counts = {}
        
        # 削除対象テーブルのリスト
        tables = self.DELETE_ORDER.copy()
        if not include_sanshi:
            tables.remove('SanshiNetSuperSale')
        
        for table_name in tables:
            try:
                count_query = self._build_count_query(table_name, filters)
                result = self.db.execute_query(count_query)
                count = result[0][0] if result else 0
                counts[table_name] = count
                self.logger.log_count_result(table_name, count)
            except Exception as e:
                self.logger.log_error(f"件数集計エラー [{table_name}]: {e}")
                counts[table_name] = -1  # エラーを示す
        
        return counts
    
    def delete_records(self, filters: FilterConfig, include_sanshi: bool = False, dry_run: bool = False) -> Dict[str, int]:
        """
        レコードを削除
        
        Args:
            filters: フィルター設定
            include_sanshi: SanshiNetSuperSaleを含むかどうか
            dry_run: ドライランモード
            
        Returns:
            Dict[str, int]: テーブル名と削除件数の辞書
        """
        deleted_counts = {}
        
        # 削除対象テーブルのリスト
        tables = self.DELETE_ORDER.copy()
        if not include_sanshi:
            tables.remove('SanshiNetSuperSale')
        
        for table_name in tables:
            try:
                # 削除前の件数確認
                count_query = self._build_count_query(table_name, filters)
                result = self.db.execute_query(count_query)
                target_count = result[0][0] if result else 0
                
                if target_count == 0:
                    self.logger.log_delete_skipped(table_name)
                    deleted_counts[table_name] = 0
                    continue
                
                self.logger.log_delete_start(table_name)
                
                if not dry_run:
                    # 実際の削除実行
                    delete_query = self._build_delete_query(table_name, filters)
                    deleted_count = self.db.execute_non_query(delete_query)
                    deleted_counts[table_name] = deleted_count
                    self.logger.log_delete_result(table_name, deleted_count)
                else:
                    # ドライランの場合は件数のみ記録
                    deleted_counts[table_name] = target_count
                    self.logger.log_delete_result(table_name, target_count)
                
            except Exception as e:
                self.logger.log_error(f"削除処理エラー [{table_name}]: {e}")
                deleted_counts[table_name] = -1  # エラーを示す
                raise  # エラーの場合は処理を中断
        
        return deleted_counts
    
    def _build_count_query(self, table_name: str, filters: FilterConfig) -> str:
        """件数確認クエリを構築"""
        base_query = f"SELECT COUNT(*) FROM {table_name}"
        where_clause = self._build_where_clause(table_name, filters)
        
        if table_name == 'ReceiptDetail':
            # ReceiptDetailは結合が必要
            return f"""
            SELECT COUNT(*)
            FROM ReceiptDetail INNER JOIN ReceiptHeader ON ReceiptDetail.ReceiptHeaderId = ReceiptHeader.Id
            {where_clause}
            """
        
        return f"{base_query} {where_clause}"
    
    def _build_delete_query(self, table_name: str, filters: FilterConfig) -> str:
        """削除クエリを構築"""
        where_clause = self._build_where_clause(table_name, filters)
        
        if table_name == 'ReceiptDetail':
            # ReceiptDetailは結合が必要
            return f"""
            DELETE FROM ReceiptDetail
            FROM ReceiptDetail INNER JOIN ReceiptHeader ON ReceiptDetail.ReceiptHeaderId = ReceiptHeader.Id
            {where_clause}
            """
        
        return f"DELETE FROM {table_name} {where_clause}"
    
    def _build_where_clause(self, table_name: str, filters: FilterConfig) -> str:
        """WHERE句を構築"""
        conditions = []
        
        # 店舗コード条件
        store_codes_str = ', '.join(map(str, filters.store_codes))
        
        # 日付条件（テーブルによって営業日/配達日が異なる）
        date_str = self._convert_date_format(filters.business_date)
        
        # テーブル別の条件設定
        if table_name in ['WBRT008x_01_Item', 'WBRT008x_02_Closing', 'WBRT008x_00_Transaction']:
            # 営業日を使用するテーブル
            conditions.append(f"店コード IN ({store_codes_str})")
            conditions.append(f"営業日 = CONVERT(DATETIME, '{date_str}', 102)")
            
        elif table_name in ['WBRT0050', 'WBRT0044', 'WBRT0042', 'ProcessManager']:
            # 配達日を使用するテーブル
            conditions.append(f"店舗コード IN ({store_codes_str})")
            conditions.append(f"配達日 = CONVERT(DATETIME, '{date_str}', 102)")
            
        elif table_name == 'ReceiptHeader':
            conditions.append(f"店舗コード IN ({store_codes_str})")
            conditions.append(f"配達日 = CONVERT(DATETIME, '{date_str}', 102)")
            
        elif table_name == 'ReceiptDetail':
            # ReceiptHeaderとの結合で条件指定
            conditions.append(f"ReceiptHeader.店舗コード IN ({store_codes_str})")
            conditions.append(f"ReceiptHeader.配達日 = CONVERT(DATETIME, '{date_str}', 102)")
            
        elif table_name == 'SanshiNetSuperSale':
            conditions.append(f"店舗ｺｰﾄﾞ IN ({store_codes_str})")
            conditions.append(f"配達日 = CONVERT(DATETIME, '{date_str}', 102)")
            
            # 号車番号の条件追加
            if filters.car_no is not None:
                conditions.append(f"号車NO = {filters.car_no}")
        
        # POS区分の条件追加（該当するテーブルのみ）
        if filters.pos_kbn is not None and table_name in ['WBRT008x_01_Item', 'WBRT008x_02_Closing', 'WBRT008x_00_Transaction']:
            conditions.append(f"POS区分 = {filters.pos_kbn}")
        
        if conditions:
            return f"WHERE {' AND '.join(conditions)}"
        else:
            return ""
    
    def _convert_date_format(self, date_str: str) -> str:
        """
        日付形式を変換 (YYYY/MM/DD -> YYYY-MM-DD)
        
        Args:
            date_str: YYYY/MM/DD形式の日付文字列
            
        Returns:
            str: YYYY-MM-DD形式の日付文字列
        """
        try:
            date_obj = datetime.strptime(date_str, '%Y/%m/%d')
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            # フォーマットエラーの場合はそのまま返す
            return date_str
