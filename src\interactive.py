"""
対話機能モジュール
削除確認プロンプト、件数表示機能を提供
"""

from typing import Dict
from .logger import ApplicationLogger


class InteractivePrompt:
    """対話機能クラス"""
    
    def __init__(self, logger: ApplicationLogger):
        """
        初期化
        
        Args:
            logger: ログ
        """
        self.logger = logger
    
    def display_count_summary(self, counts: Dict[str, int], include_sanshi: bool = False) -> int:
        """
        件数サマリーを表示
        
        Args:
            counts: テーブル別件数辞書
            include_sanshi: SanshiNetSuperSaleを含むかどうか
            
        Returns:
            int: 合計件数
        """
        print("\n" + "=" * 60)
        print("削除対象データ件数")
        print("=" * 60)
        
        total_count = 0
        has_error = False
        
        # 削除順序に従って表示
        from .cleanup_engine import CleanupEngine
        tables = CleanupEngine.DELETE_ORDER.copy()
        if not include_sanshi:
            tables.remove('SanshiNetSuperSale')
        
        for table_name in tables:
            count = counts.get(table_name, 0)
            if count == -1:
                print(f"{table_name:<25}: エラー")
                has_error = True
            else:
                print(f"{table_name:<25}: {count:>10,} 件")
                total_count += count
        
        print("-" * 60)
        if has_error:
            print(f"{'合計':<25}: エラーあり")
        else:
            print(f"{'合計':<25}: {total_count:>10,} 件")
        print("=" * 60)
        
        return total_count
    
    def confirm_deletion(self, total_count: int, dry_run: bool = False) -> bool:
        """
        削除確認プロンプト
        
        Args:
            total_count: 合計削除件数
            dry_run: ドライランモード
            
        Returns:
            bool: 削除実行するかどうか
        """
        if total_count == 0:
            print("\n削除対象のデータがありません。処理を終了します。")
            self.logger.log_info("削除対象データなしのため処理終了")
            return False
        
        if dry_run:
            print(f"\n*** ドライランモード ***")
            print(f"上記 {total_count:,} 件のデータが削除対象です。")
            print("実際の削除は行いません。")
            return True
        
        print(f"\n上記 {total_count:,} 件のデータを削除します。")
        print("この操作は取り消すことができません。")
        
        while True:
            try:
                response = input("\n削除を実行しますか？ (Y/N): ").strip().upper()
                
                if response == 'Y':
                    self.logger.log_info("ユーザーが削除実行を承認")
                    return True
                elif response == 'N':
                    print("削除処理をキャンセルしました。")
                    self.logger.log_user_cancelled()
                    return False
                else:
                    print("Y または N を入力してください。")
                    
            except KeyboardInterrupt:
                print("\n\n削除処理をキャンセルしました。")
                self.logger.log_user_cancelled()
                return False
            except EOFError:
                print("\n\n削除処理をキャンセルしました。")
                self.logger.log_user_cancelled()
                return False
    
    def display_deletion_progress(self, table_name: str, current: int, total: int) -> None:
        """
        削除進捗を表示
        
        Args:
            table_name: テーブル名
            current: 現在の処理番号
            total: 総処理数
        """
        progress = (current / total) * 100
        print(f"[{current}/{total}] ({progress:.1f}%) {table_name} を処理中...")
    
    def display_completion_summary(self, deleted_counts: Dict[str, int], dry_run: bool = False) -> None:
        """
        完了サマリーを表示
        
        Args:
            deleted_counts: テーブル別削除件数辞書
            dry_run: ドライランモード
        """
        print("\n" + "=" * 60)
        if dry_run:
            print("ドライラン結果")
        else:
            print("削除処理結果")
        print("=" * 60)
        
        total_deleted = 0
        has_error = False
        
        # 削除順序に従って表示
        from .cleanup_engine import CleanupEngine
        tables = CleanupEngine.DELETE_ORDER.copy()
        
        for table_name in tables:
            if table_name not in deleted_counts:
                continue
                
            count = deleted_counts[table_name]
            if count == -1:
                print(f"{table_name:<25}: エラー")
                has_error = True
            else:
                action = "削除対象" if dry_run else "削除"
                print(f"{table_name:<25}: {count:>10,} 件{action}")
                total_deleted += count
        
        print("-" * 60)
        if has_error:
            print(f"{'合計':<25}: エラーあり")
        else:
            action = "削除対象" if dry_run else "削除"
            print(f"{'合計':<25}: {total_deleted:>10,} 件{action}")
        print("=" * 60)
        
        if dry_run:
            print("\n*** ドライランモードのため、実際の削除は行われていません ***")
        else:
            print(f"\n削除処理が完了しました。合計 {total_deleted:,} 件のデータを削除しました。")
    
    def display_error(self, error_message: str) -> None:
        """
        エラーメッセージを表示
        
        Args:
            error_message: エラーメッセージ
        """
        print(f"\nエラー: {error_message}")
        self.logger.log_error(error_message)
    
    def display_warning(self, warning_message: str) -> None:
        """
        警告メッセージを表示
        
        Args:
            warning_message: 警告メッセージ
        """
        print(f"\n警告: {warning_message}")
        self.logger.log_warning(warning_message)
