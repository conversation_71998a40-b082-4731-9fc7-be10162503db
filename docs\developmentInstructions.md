# データ再処理用バッチ（削除処理） 開発仕様書 v1.0

## 1. 目的

特定店舗・特定日付（営業日／配達日）・任意の追加条件（POS区分／号車番号）を基に、再処理の前段として関連テーブルの既存データを安全に削除するためのバッチプログラム（以下、本PG）を提供する。

## 2. 対象範囲

* 既存データの **削除** のみを行う。再投入／再計算は本PGの範囲外。
* 削除対象テーブルは以下のサンプルクエリに基づく。
* 店舗コード（複数可）、営業日、（任意で）POS区分、号車番号をキーに削除。
* `SanshiNetSuperSale` の削除は、不正データ投入の可能性があり、**データ再取込を行う場合のみ実行**する特例処理とする。

## 3. 前提／制約

* 言語: Python 3.11 以降。
* 実行形態: 単体 EXE（PyInstaller 等でパッケージ化）。
* DB: Microsoft SQL Server（バージョン 2016 以上を想定）。
* 接続方式: ODBC（pyodbc）または pymssql（既定は pyodbc）。
* 権限: 対象テーブルへの DELETE 権限、トランザクション制御権限が必要。
* 運用: バッチ時間帯に実行し、オンライン処理と競合しないよう調整。

## 4. 実行方法

```bash
DataCleanup.exe -c C:\batch\config\cleanup.yml        # 通常実行
DataCleanup.exe -c C:\batch\config\cleanup.yml --dry-run  # ドライラン（件数確認のみ）
```

* `--dry-run` 指定時は削除件数の集計・表示とログ出力のみを行い、実削除はしない。

## 5. 設定ファイル仕様

### 5.1 形式（YAML 例）

```yaml
app:
  log_path:  "D:/logs/data_cleanup/%Y%m%d_cleanup.log"
  log_level: "INFO"
  prompt_on_delete: true
  delete_sanshi_only_on_reimport: true
filters:
  store_codes: [100, 200]
  business_date: "2025/08/05"
  pos_kbn: null
  car_no: 3
database:
  driver: "ODBC Driver 18 for SQL Server"
  host: "sqlsvr01.example.local"
  port: 1433
  database: "RetailDB"
  user: "batch_user"
  password: "********"
  encrypt: true
  trust_server_certificate: false
  login_timeout_sec: 30
  command_timeout_sec: 600
```

### 5.2 バリデーション

* 店舗コード: 1〜9999 の整数、最大件数 100。
* 日付: `YYYY/MM/DD` 形式。
* POS 区分・号車番号: null または 0〜9 の整数 1 桁。
* DB 接続情報は必須。

## 6. ログ仕様

* ローテーション（10世代、10MB/世代）
* 実行条件、件数、削除結果、エラーを記録

## 7. 対話仕様

1. 件数表示
2. `Y` で削除実行、`N` で中断（終了コード 2）
3. 0 件はスキップ

## 8. 処理フロー

1. 設定ファイル読込
2. DB 接続
3. 件数集計
4. 確認プロンプト
5. 削除順序に従って実行（SanshiNetSuperSale はフラグが true の場合のみ）
6. コミットまたはロールバック

## 9. 削除順序

1. ReceiptDetail
2. ReceiptHeader
3. WBRT008x\_01\_Item
4. WBRT008x\_02\_Closing
5. WBRT008x\_00\_Transaction
6. WBRT0050
7. WBRT0044
8. WBRT0042
9. ProcessManager
10. SanshiNetSuperSale（再取込時のみ）

## 10. サンプル SQL（提供クエリ）

```sql
DELETE FROM WBRT008x_01_Item
WHERE (店コード = 100 OR 店コード = 200) AND (営業日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102));

DELETE FROM WBRT008x_02_Closing
WHERE (店コード = 100 OR 店コード = 200) AND (営業日 = CONVERT(DATETIME, '2025-08-04 00:00:00', 102));

DELETE FROM WBRT0050
WHERE (店舗コード = 100 OR 店舗コード = 200) AND (配達日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102));

DELETE FROM WBRT008x_00_Transaction
WHERE (店コード = 100 OR 店コード = 200) AND (営業日 = CONVERT(DATETIME, '2025-08-04 00:00:00', 102));

DELETE FROM WBRT0044
WHERE (配達日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102)) AND (店舗コード = 100 OR 店舗コード = 200);

DELETE FROM WBRT0042
WHERE (配達日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102)) AND (店舗コード = 100 OR 店舗コード = 200);

DELETE FROM ProcessManager
WHERE (配達日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102)) AND (店舗コード = 100 OR 店舗コード = 200);

DELETE FROM ReceiptDetail
FROM ReceiptDetail INNER JOIN ReceiptHeader ON ReceiptDetail.ReceiptHeaderId = ReceiptHeader.Id
WHERE (ReceiptHeader.店舗コード = 100 OR ReceiptHeader.店舗コード = 200) AND (ReceiptHeader.配達日 = CONVERT(DATETIME, '2025-08-05 00:00:00', 102));

DELETE FROM ReceiptHeader
WHERE (店舗コード = 100 OR 店舗コード = 200) AND (配達日 = CONVERT(DATETIME, '2025-08-04 00:00:00', 102));

DELETE FROM SanshiNetSuperSale
WHERE (配達日 = CONVERT(DATETIME,'2025-08-05 00:00:00',102))
  AND (店舗ｺｰﾄﾞ IN (100,200))
  AND (号車NO = 3)
  -- 再取込時のみ実行
```

## 11. 終了コード

* 0: 正常終了
* 1: 異常終了
* 2: ユーザー中断

---

以上。
