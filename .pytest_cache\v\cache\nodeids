["tests/test_cleanup_engine.py::TestCleanupEngine::test_build_where_clause_receipt_detail", "tests/test_cleanup_engine.py::TestCleanupEngine::test_build_where_clause_sanshi", "tests/test_cleanup_engine.py::TestCleanupEngine::test_build_where_clause_wbrt_tables", "tests/test_cleanup_engine.py::TestCleanupEngine::test_convert_date_format", "tests/test_cleanup_engine.py::TestCleanupEngine::test_count_target_records", "tests/test_cleanup_engine.py::TestCleanupEngine::test_count_target_records_with_sanshi", "tests/test_cleanup_engine.py::TestCleanupEngine::test_delete_order", "tests/test_cleanup_engine.py::TestCleanupEngine::test_delete_records_dry_run", "tests/test_cleanup_engine.py::TestCleanupEngine::test_delete_records_normal", "tests/test_cleanup_engine.py::TestCleanupEngine::test_delete_records_zero_count", "tests/test_cleanup_engine.py::TestCleanupEngine::test_error_handling_in_count", "tests/test_config.py::TestConfigLoader::test_invalid_business_date", "tests/test_config.py::TestConfigLoader::test_invalid_log_level", "tests/test_config.py::TestConfigLoader::test_invalid_port", "tests/test_config.py::TestConfigLoader::test_invalid_store_codes", "tests/test_config.py::TestConfigLoader::test_invalid_yaml", "tests/test_config.py::TestConfigLoader::test_load_nonexistent_file", "tests/test_config.py::TestConfigLoader::test_load_valid_config", "tests/test_config.py::TestConfigLoader::test_missing_required_section", "tests/test_integration.py::TestIntegration::test_application_config_error", "tests/test_integration.py::TestIntegration::test_application_database_error", "tests/test_integration.py::TestIntegration::test_application_dry_run_mode", "tests/test_integration.py::TestIntegration::test_application_user_cancellation", "tests/test_integration.py::TestIntegration::test_application_zero_records", "tests/test_integration.py::TestIntegration::test_command_line_parser", "tests/test_integration.py::TestIntegration::test_invalid_command_line_args", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_confirm_settings_update_keyboard_interrupt", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_confirm_settings_update_no", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_confirm_settings_update_yes", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_business_date_invalid_format", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_business_date_no_change", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_business_date_valid_input", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_car_no_no_change", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_car_no_set_null", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_car_no_valid_input", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_pos_kbn_no_change", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_pos_kbn_set_null", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_pos_kbn_valid_input", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_store_codes_invalid_input", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_store_codes_no_change", "tests/test_interactive_config.py::TestInteractiveConfigInput::test_prompt_store_codes_valid_input"]