"""
統合テスト
"""

import unittest
import tempfile
import os
import sys
from unittest.mock import patch, Mock
import yaml

# テスト対象のモジュールをインポート
from src.main import DataCleanupApplication
from src.cli import CommandLineParser


class TestIntegration(unittest.TestCase):
    """統合テストクラス"""
    
    def setUp(self):
        """テスト前の準備"""
        self.temp_dir = tempfile.mkdtemp()
        self.app = DataCleanupApplication()
    
    def tearDown(self):
        """テスト後のクリーンアップ"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_config(self):
        """テスト用設定ファイルを作成"""
        config_data = {
            'app': {
                'log_path': os.path.join(self.temp_dir, 'test.log'),
                'log_level': 'INFO',
                'prompt_on_delete': False,  # テスト用にプロンプト無効
                'delete_sanshi_only_on_reimport': False
            },
            'filters': {
                'store_codes': [100, 200],
                'business_date': '2025/08/05',
                'pos_kbn': None,
                'car_no': 3
            },
            'database': {
                'driver': 'ODBC Driver 18 for SQL Server',
                'host': 'localhost',
                'port': 1433,
                'database': 'TestDB',
                'user': 'test_user',
                'password': 'test_pass',
                'encrypt': True,
                'trust_server_certificate': False,
                'login_timeout_sec': 30,
                'command_timeout_sec': 600
            }
        }
        
        config_path = os.path.join(self.temp_dir, 'test_config.yml')
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        return config_path
    
    def test_command_line_parser(self):
        """コマンドライン引数解析のテスト"""
        parser = CommandLineParser()

        # 正常なケース
        config_path, dry_run, verbose, interactive = parser.parse_args(['-c', 'test.yml'])
        self.assertEqual(config_path, 'test.yml')
        self.assertFalse(dry_run)
        self.assertFalse(verbose)
        self.assertFalse(interactive)

        # ドライランオプション付き
        config_path, dry_run, verbose, interactive = parser.parse_args(['-c', 'test.yml', '--dry-run'])
        self.assertTrue(dry_run)

        # 詳細ログオプション付き
        config_path, dry_run, verbose, interactive = parser.parse_args(['-c', 'test.yml', '--verbose'])
        self.assertTrue(verbose)

        # 対話式オプション付き
        config_path, dry_run, verbose, interactive = parser.parse_args(['-c', 'test.yml', '--interactive'])
        self.assertTrue(interactive)
    
    @patch('src.main.DatabaseConnection')
    def test_application_dry_run_mode(self, mock_db_class):
        """アプリケーションのドライランモードテスト"""
        # モックの設定
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.execute_query.return_value = [(10,)]  # 件数10件

        # コンテキストマネージャーのサポート
        mock_db.__enter__ = Mock(return_value=mock_db)
        mock_db.__exit__ = Mock(return_value=None)
        mock_db.transaction.return_value.__enter__ = Mock(return_value=mock_db)
        mock_db.transaction.return_value.__exit__ = Mock(return_value=None)

        # テスト用設定ファイル作成
        config_path = self.create_test_config()

        # ドライランモードで実行
        args = ['-c', config_path, '--dry-run']
        exit_code = self.app.run(args)

        # 検証
        self.assertEqual(exit_code, 0)  # 正常終了
        # ドライランなので実際の削除は実行されない
        mock_db.execute_non_query.assert_not_called()
    
    @patch('src.main.DatabaseConnection')
    def test_application_zero_records(self, mock_db_class):
        """削除対象が0件の場合のテスト"""
        # モックの設定
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.execute_query.return_value = [(0,)]  # 件数0件

        # コンテキストマネージャーのサポート
        mock_db.__enter__ = Mock(return_value=mock_db)
        mock_db.__exit__ = Mock(return_value=None)

        # テスト用設定ファイル作成
        config_path = self.create_test_config()

        # 実行
        args = ['-c', config_path]
        exit_code = self.app.run(args)

        # 検証
        self.assertEqual(exit_code, 0)  # 正常終了
        # 件数0なので削除処理は実行されない
        mock_db.execute_non_query.assert_not_called()
    
    def test_application_config_error(self):
        """設定ファイルエラーのテスト"""
        # 存在しない設定ファイルを指定
        args = ['-c', 'nonexistent.yml']
        exit_code = self.app.run(args)
        
        # 検証
        self.assertEqual(exit_code, 1)  # 異常終了
    
    @patch('src.main.DatabaseConnection')
    def test_application_database_error(self, mock_db_class):
        """データベースエラーのテスト"""
        # モックでデータベースエラーを発生させる
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.connect.side_effect = Exception("Database connection failed")

        # テスト用設定ファイル作成
        config_path = self.create_test_config()

        # 実行
        args = ['-c', config_path]
        exit_code = self.app.run(args)

        # 検証
        self.assertEqual(exit_code, 1)  # 異常終了
    
    @patch('src.main.DatabaseConnection')
    @patch('builtins.input', return_value='N')  # ユーザー入力をモック
    def test_application_user_cancellation(self, mock_input, mock_db_class):
        """ユーザーキャンセルのテスト"""
        # モックの設定
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.execute_query.return_value = [(10,)]  # 件数10件
        
        # プロンプト有効の設定ファイル作成
        config_data = {
            'app': {
                'log_path': os.path.join(self.temp_dir, 'test.log'),
                'log_level': 'INFO',
                'prompt_on_delete': True,  # プロンプト有効
                'delete_sanshi_only_on_reimport': False
            },
            'filters': {
                'store_codes': [100],
                'business_date': '2025/08/05'
            },
            'database': {
                'driver': 'test',
                'host': 'localhost',
                'port': 1433,
                'database': 'test',
                'user': 'test',
                'password': 'test'
            }
        }
        
        config_path = os.path.join(self.temp_dir, 'test_config.yml')
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        # 実行
        args = ['-c', config_path]
        exit_code = self.app.run(args)
        
        # 検証
        self.assertEqual(exit_code, 2)  # ユーザー中断
        # キャンセルされたので削除処理は実行されない
        mock_db.execute_non_query.assert_not_called()
    
    def test_invalid_command_line_args(self):
        """無効なコマンドライン引数のテスト"""
        # 必須引数なし
        with patch('sys.exit') as mock_exit:
            parser = CommandLineParser()
            parser.parse_args([])  # 引数なし
            mock_exit.assert_called_with(2)  # argparseのエラーは終了コード2


if __name__ == '__main__':
    unittest.main()
